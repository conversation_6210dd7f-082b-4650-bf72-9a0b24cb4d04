import { CreateAppointmentDto } from './dto/create/create-appointment.dto';
import { AppointmentEntity } from './entities/appointment.entity';
import { Repository } from 'typeorm';
import { AppointmentDoctorsEntity } from './entities/appointment-doctor.entity';
import { EnumAppointmentStatus } from './enums/enum-appointment-status';
import { AppointmentDetailsEntity } from './entities/appointment-details.entity';
import { UpdateAppointmentDetailsDto } from './dto/details/update-appointment-details.dto';
import { UpdateAppointmentsDto } from './dto/create/update-appointment.dto';
import { UpdateAppointmentFeildsDto } from './dto/create/update-appointmentField.dto';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { TasksService } from '../tasks/tasks.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { EmrService } from '../emr/emr.service';
import { PatientRemindersService } from '../patient-reminders/patient-reminder.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { EnumAppointmentType } from './enums/enum-appointment-type';
import { AvailabilityService } from '../availability/availability.service';
import { LongTermMedicationsService } from '../long-term-medications/long-term-medications.service';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';
import { GoogleCalendarService } from '../google-calendar/google-calendar.service';
import { GoogleCalendarCacheService } from '../google-calendar/google-calendar-cache.service';
import { User } from '../users/entities/user.entity';
export declare class AppointmentsService {
    private appointmentRepository;
    private appointmentDoctorsRepository;
    private appointmentDetailsRepository;
    private userRepository;
    private readonly mailService;
    private readonly tasksService;
    private readonly logger;
    private readonly whatsappService;
    private readonly remindersService;
    private readonly emrService;
    private readonly sqsService;
    private readonly globalReminderService;
    private readonly availabilityService;
    private readonly longTermMedicationsService;
    private readonly appointmentGateway;
    private readonly googleCalendarService;
    private readonly googleCalendarCache;
    constructor(appointmentRepository: Repository<AppointmentEntity>, appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>, appointmentDetailsRepository: Repository<AppointmentDetailsEntity>, userRepository: Repository<User>, mailService: SESMailService, tasksService: TasksService, logger: WinstonLogger, whatsappService: WhatsappService, remindersService: PatientRemindersService, emrService: EmrService, sqsService: SqsService, globalReminderService: GlobalReminderService, availabilityService: AvailabilityService, longTermMedicationsService: LongTermMedicationsService, appointmentGateway: AppointmentGateway, googleCalendarService: GoogleCalendarService, googleCalendarCache: GoogleCalendarCacheService);
    createAppointment(createAppointmentDto: CreateAppointmentDto, brandId: string, createdByUserId?: string): Promise<AppointmentEntity>;
    private syncOnAppointmentCreate;
    getAllAppointments(page?: number, limit?: number, orderBy?: string, date?: string, search?: string, doctors?: Array<any>, status?: Array<any>, onlyPrimary?: boolean, clinicId?: string, includeGoogleEvents?: boolean, viewerUserId?: string): Promise<{
        appointments: {
            weight: string | undefined;
            appointmentDoctors: {
                id: string;
                appointmentId: string;
                doctorId: string;
                primary: boolean;
                doctor: {
                    id: string;
                    firstName: string;
                    lastName: string;
                    email: string;
                };
            }[];
            id: string;
            clinicId: string;
            brandId: string;
            patientId: string;
            roomId: string;
            reason: string;
            type: EnumAppointmentType;
            triage: import("./enums/enum-appointment-triage").EnumAppointmentTriage;
            date: Date;
            startTime: Date;
            endTime: Date;
            deletedAt?: Date;
            notes?: object;
            preVisitQuestions?: object;
            reminderTracking?: {
                lastProcessedAt?: string;
                emailStatus?: "sent" | "failed";
                emailSentAt?: string;
                whatsappStatus?: "sent" | "failed";
                whatsappSentAt?: string;
                retryCount?: number;
                error?: string;
                ownerNotifications?: {
                    [ownerId: string]: {
                        emailStatus?: "sent" | "failed";
                        emailSentAt?: string;
                        emailError?: string;
                        whatsappStatus?: "sent" | "failed";
                        whatsappSentAt?: string;
                        whatsappError?: string;
                    };
                };
            };
            isBlocked?: boolean;
            status?: EnumAppointmentStatus;
            createdAt?: Date;
            updatedAt?: Date;
            createdBy?: string;
            updatedBy?: string;
            initialNotes?: string;
            checkinTime: Date;
            receivingCareTime: Date;
            checkoutTime: Date;
            patient: import("../patients/entities/patient.entity").Patient;
            clinic: import("../clinics/entities/clinic.entity").ClinicEntity;
            room: import("../clinics/entities/clinic-room.entity").ClinicRoomEntity;
            appointmentDetails: AppointmentDetailsEntity;
            labReports: import("../clinic-lab-report/entities/lab-report.entity").LabReport[];
            patientVaccinations?: import("../patient-vaccinations/entities/patient-vaccinations.entity").PatientVaccination[];
            cart: import("../carts/entites/cart.entity").CartEntity;
            emr?: import("../emr/entities/emr.entity").Emr;
            diagnosticNotes?: import("../diagnostic-notes-templates/entities/diagnostic-note.entity").DiagnosticNote[];
            mode: string;
            googleEventId?: string;
        }[];
        total: number;
    }>;
    getAppointmentsForPatient(patientId: string, includeDeleted?: boolean): Promise<AppointmentEntity[]>;
    /**
     * Helper method to inject appointmentId into appointment details
     * This adds appointmentId to the details object without saving it to the database
     * If appointment details are null, it provides default treatment details
     */
    private injectAppointmentIdIntoDetails;
    getAppointmentDetails(appointmentId: string): Promise<{
        appointmentDoctors: {
            id: string;
            appointmentId: string;
            doctorId: string;
            primary: boolean;
            createdBy: string | undefined;
            updatedBy: string | undefined;
            createdAt: Date;
            updatedAt: Date;
            doctor: {
                id: string;
                firstName: string;
                lastName: string;
                email: string;
            };
        }[];
        id: string;
        clinicId: string;
        brandId: string;
        patientId: string;
        roomId: string;
        reason: string;
        type: EnumAppointmentType;
        triage: import("./enums/enum-appointment-triage").EnumAppointmentTriage;
        date: Date;
        startTime: Date;
        endTime: Date;
        deletedAt?: Date;
        weight?: number;
        notes?: object;
        preVisitQuestions?: object;
        reminderTracking?: {
            lastProcessedAt?: string;
            emailStatus?: "sent" | "failed";
            emailSentAt?: string;
            whatsappStatus?: "sent" | "failed";
            whatsappSentAt?: string;
            retryCount?: number;
            error?: string;
            ownerNotifications?: {
                [ownerId: string]: {
                    emailStatus?: "sent" | "failed";
                    emailSentAt?: string;
                    emailError?: string;
                    whatsappStatus?: "sent" | "failed";
                    whatsappSentAt?: string;
                    whatsappError?: string;
                };
            };
        };
        isBlocked?: boolean;
        status?: EnumAppointmentStatus;
        createdAt?: Date;
        updatedAt?: Date;
        createdBy?: string;
        updatedBy?: string;
        initialNotes?: string;
        checkinTime: Date;
        receivingCareTime: Date;
        checkoutTime: Date;
        patient: import("../patients/entities/patient.entity").Patient;
        clinic: import("../clinics/entities/clinic.entity").ClinicEntity;
        room: import("../clinics/entities/clinic-room.entity").ClinicRoomEntity;
        appointmentDetails: AppointmentDetailsEntity;
        labReports: import("../clinic-lab-report/entities/lab-report.entity").LabReport[];
        patientVaccinations?: import("../patient-vaccinations/entities/patient-vaccinations.entity").PatientVaccination[];
        cart: import("../carts/entites/cart.entity").CartEntity;
        emr?: import("../emr/entities/emr.entity").Emr;
        diagnosticNotes?: import("../diagnostic-notes-templates/entities/diagnostic-note.entity").DiagnosticNote[];
        mode: string;
        googleEventId?: string;
    }>;
    updateAppointmentDetails(id: string, updateAppointmentDetailsDto: UpdateAppointmentDetailsDto): Promise<{
        status: boolean;
    } | AppointmentDetailsEntity>;
    /**
     * Helper method to filter out null and undefined values from an object
     * This prevents saving null data to the database
     */
    private filterNullValues;
    updateAppointmentStatus(id: string, updateAppointmentDto: UpdateAppointmentsDto, invoiceId?: string): Promise<AppointmentEntity>;
    convertToUTCDate: (date: Date) => Date;
    updateAppointment(id: string, updateAppointmentFieldsDto: UpdateAppointmentFeildsDto): Promise<AppointmentEntity>;
    /**
     * Synchronize appointment update with Google Calendar
     */
    private syncOnAppointmentUpdate;
    /**
     * Fetch Google Calendar events for a specific date and convert them to appointment format
     */
    private fetchGoogleEventsForDate;
    /**
     * Convert a Google Calendar event to appointment format
     */
    private convertGoogleEventToAppointment;
    private isValidStatusTransition;
    deleteAppointment(id: string): Promise<{
        status: boolean;
    }>;
    checkPatientOnGoingAppointment(patientId: string): Promise<{
        hasOngoingAppointment: boolean;
        appointment: AppointmentEntity | null;
    }>;
    downloadTodaysAppointment(clinicId: string, date: string): Promise<string>;
    /**
     * Cron job that runs every hour to check for missed appointments
     * Finds appointments that are:
     * 1. Not in Completed, Cancelled, or Missed status
     * 2. More than 16 hours old (based on combined date and start time)
     * And marks them as Missed
     */
    handleMissedAppointments(): Promise<void>;
    createImpromptuAppointment(data: {
        clinicId: string;
        patientId: string;
        brandId: string;
        date: Date;
        startTime: Date;
        endTime: Date;
        reason: string;
        type: EnumAppointmentType;
        status: EnumAppointmentStatus;
        createdBy?: string;
    }): Promise<AppointmentEntity>;
    /**
     * Generate default treatment details structure for new appointments
     * This initializes the appointment details with the standard SOAP format
     * and includes long-term medications in the prescription list
     */
    private generateDefaultTreatmentDetails;
    /**
     * Emit socket updates based on call site ID to sync real-time changes
     */
    private emitSocketUpdatesForCallSite;
    /**
     * Emit plan section updates
     */
    private emitPlanUpdate;
    /**
     * Emit objective lab report updates
     */
    private emitObjectiveLabReportUpdate;
    /**
     * Send email and WhatsApp notifications when an appointment is updated or cancelled.
     * For now creation notifications stay where they are (createAppointment).
     */
    private sendAppointmentNotifications;
}
