"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const appointment_entity_1 = require("./entities/appointment.entity");
const typeorm_2 = require("typeorm");
const appointment_doctor_entity_1 = require("./entities/appointment-doctor.entity");
const enum_appointment_status_1 = require("./enums/enum-appointment-status");
const appointment_details_entity_1 = require("./entities/appointment-details.entity");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const moment = require("moment-timezone");
const tasks_service_1 = require("../tasks/tasks.service");
const create_task_dto_1 = require("../tasks/dto/create-task.dto");
const schedule_1 = require("@nestjs/schedule");
const constants_1 = require("../utils/constants");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const get_login_url_1 = require("../utils/common/get-login-url");
const whatsapp_service_1 = require("../utils/whatsapp-integration/whatsapp.service");
const mail_template_generator_1 = require("../utils/mail-generator/mail-template-generator");
const whatsapp_template_generator_1 = require("../utils/communicatoins/whatsapp-template-generator");
const emr_service_1 = require("../emr/emr.service");
const todays_appoitments_1 = require("../utils/pdfs/todays-appoitments");
const generatePdf_1 = require("../utils/generatePdf");
const path = require("path");
const fs = require("fs/promises");
const reminder_enum_1 = require("../patient-reminders/enums/reminder.enum");
const patient_reminder_service_1 = require("../patient-reminders/patient-reminder.service");
const invoice_service_1 = require("../invoice/invoice.service");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const global_reminders_service_1 = require("../patient-global-reminders/global-reminders.service");
const enum_appointment_type_1 = require("./enums/enum-appointment-type");
const availability_service_1 = require("../availability/availability.service");
const template_helper_util_1 = require("../utils/common/template-helper.util");
const uuid_1 = require("uuid");
const long_term_medications_service_1 = require("../long-term-medications/long-term-medications.service");
const socket_appointment_gateway_1 = require("../socket/socket.appointment.gateway");
const google_calendar_service_1 = require("../google-calendar/google-calendar.service");
const google_calendar_cache_service_1 = require("../google-calendar/google-calendar-cache.service");
const user_entity_1 = require("../users/entities/user.entity");
let AppointmentsService = class AppointmentsService {
    constructor(appointmentRepository, appointmentDoctorsRepository, appointmentDetailsRepository, userRepository, mailService, tasksService, logger, whatsappService, remindersService, emrService, sqsService, globalReminderService, availabilityService, longTermMedicationsService, appointmentGateway, googleCalendarService, googleCalendarCache) {
        this.appointmentRepository = appointmentRepository;
        this.appointmentDoctorsRepository = appointmentDoctorsRepository;
        this.appointmentDetailsRepository = appointmentDetailsRepository;
        this.userRepository = userRepository;
        this.mailService = mailService;
        this.tasksService = tasksService;
        this.logger = logger;
        this.whatsappService = whatsappService;
        this.remindersService = remindersService;
        this.emrService = emrService;
        this.sqsService = sqsService;
        this.globalReminderService = globalReminderService;
        this.availabilityService = availabilityService;
        this.longTermMedicationsService = longTermMedicationsService;
        this.appointmentGateway = appointmentGateway;
        this.googleCalendarService = googleCalendarService;
        this.googleCalendarCache = googleCalendarCache;
        this.convertToUTCDate = (date) => {
            return moment(date).utc().toDate();
        };
    }
    async createAppointment(createAppointmentDto, brandId, createdByUserId) {
        var _a;
        createAppointmentDto.status = enum_appointment_status_1.EnumAppointmentStatus.Scheduled;
        const appointment = this.appointmentRepository.create({
            ...createAppointmentDto,
            date: new Date(createAppointmentDto.date),
            brandId,
            createdBy: createdByUserId
        });
        const createdAppointment = await this.appointmentRepository.save(appointment);
        if (!createdAppointment) {
            throw new common_1.InternalServerErrorException('Failed in creating an appointment!');
        }
        const createAppointmentDetails = await this.appointmentDetailsRepository.save({
            appointmentId: createdAppointment.id,
            details: await this.generateDefaultTreatmentDetails(createdAppointment.id, createAppointmentDto.patientId)
        });
        if (!createAppointmentDetails) {
            throw new common_1.InternalServerErrorException('Failed in creating appointment details!');
        }
        const doctorPromises = createAppointmentDto.doctorIds.map(async (doctorId) => {
            const createdAppointentDoctorEntry = await this.appointmentDoctorsRepository.save({
                appointmentId: createdAppointment.id,
                doctorId: doctorId,
                primary: true
            });
            if (!createdAppointentDoctorEntry) {
                throw new common_1.InternalServerErrorException('Failed in creating an appointment..!!!');
            }
        });
        const providerPromises = createAppointmentDto === null || createAppointmentDto === void 0 ? void 0 : createAppointmentDto.providerIds.map(async (id) => {
            const createdAppointentDoctorEntry = await this.appointmentDoctorsRepository.save({
                appointmentId: createdAppointment.id,
                doctorId: id,
                primary: false
            });
            if (!createdAppointentDoctorEntry) {
                throw new common_1.InternalServerErrorException('Failed in creating an appointment..!!!');
            }
        });
        await Promise.all([...doctorPromises, ...providerPromises]);
        // After ensuring appointment-doctor links are saved, sync with Google Calendar
        await this.syncOnAppointmentCreate(createdAppointment, createdByUserId);
        const appointmentDetails = (await this.appointmentRepository.findOne({
            where: { id: appointment.id },
            relations: [
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user',
                'room',
                'appointmentDetails',
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'clinic',
                'clinic.brand'
            ]
        }));
        // Handle midnight-crossing appointments
        const startTime = new Date(`${createAppointmentDto.date}T${createAppointmentDto.startTime}`);
        const endTime = new Date(`${createAppointmentDto.date}T${createAppointmentDto.endTime}`);
        if (endTime < startTime) {
            // This is a midnight-crossing appointment
            this.logger.log('Midnight-crossing appointment detected', {
                appointmentId: createdAppointment.id,
                date: createAppointmentDto.date
            });
            // TODO: In the future, we'll need to handle this by affecting availability on two days
            // Current implementation will treat this as same-day appointment
        }
        // Update availability for all involved doctors
        try {
            appointmentDetails.date = moment
                .utc(createAppointmentDto.date, 'DD-MMM-YYYY')
                .toDate();
            await this.availabilityService.handleAppointmentChange(appointmentDetails, 'create');
        }
        catch (error) {
            this.logger.error('Error updating availability after creating appointment', {
                appointmentId: createdAppointment.id,
                error
            });
            // Don't fail the appointment creation if availability update fails
            // The availability system will recover through scheduled validation
        }
        (_a = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.patient) === null || _a === void 0 ? void 0 : _a.patientOwners.forEach((patientOwner) => {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5;
            // Use ownerBrand and nested globalOwner for patient owner details
            const ownerFirstName = ((_a = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName) || '';
            const ownerLastName = ((_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName) || '';
            const ownerMobileNumber = `${((_d = (_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.globalOwner) === null || _d === void 0 ? void 0 : _d.countryCode) || ''}${((_f = (_e = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _e === void 0 ? void 0 : _e.globalOwner) === null || _f === void 0 ? void 0 : _f.phoneNumber) || ''}`;
            const { body, subject, toMailAddress } = (0, mail_template_generator_1.appointmentConfirmartionMailGenerator)({
                appointmentdate: moment(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.date).format('MMMM Do YYYY'),
                appointmentTime: `${moment(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
                brandName: (_h = (_g = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _g === void 0 ? void 0 : _g.brand) === null || _h === void 0 ? void 0 : _h.name,
                contactInformation: ((_l = (_k = (_j = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers) === null || _k === void 0 ? void 0 : _k[0]) === null || _l === void 0 ? void 0 : _l.number) || 'provided contact no.',
                email: (_m = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _m === void 0 ? void 0 : _m.email,
                firstname: ownerFirstName,
                lastName: ownerLastName,
                clinicAddress: `${((_o = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _o === void 0 ? void 0 : _o.addressLine1) || ''} ${((_p = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _p === void 0 ? void 0 : _p.addressLine2) || ''}, ${((_q = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _q === void 0 ? void 0 : _q.city) || ''}, ${((_r = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _r === void 0 ? void 0 : _r.state) || ''} ${((_s = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _s === void 0 ? void 0 : _s.addressPincode) || ''}, ${((_t = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _t === void 0 ? void 0 : _t.country) || ''}`
            });
            if (ownerMobileNumber) {
                const templateArgs = {
                    appointmentDate: moment(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.date).format('MMMM Do YYYY'),
                    appointmentTime: `${moment(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
                    brandName: (_v = (_u = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _u === void 0 ? void 0 : _u.brand) === null || _v === void 0 ? void 0 : _v.name,
                    contactInformation: (_y = (_x = (_w = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _w === void 0 ? void 0 : _w.phoneNumbers) === null || _x === void 0 ? void 0 : _x[0]) === null || _y === void 0 ? void 0 : _y.number,
                    clientName: `${ownerFirstName} ${ownerLastName}`,
                    mobileNumber: ownerMobileNumber,
                    clinicAddress: `${((_z = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _z === void 0 ? void 0 : _z.addressLine1) || ''} ${((_0 = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _0 === void 0 ? void 0 : _0.addressLine2) || ''}, ${((_1 = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _1 === void 0 ? void 0 : _1.city) || ''}, ${((_2 = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _2 === void 0 ? void 0 : _2.state) || ''} ${((_3 = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _3 === void 0 ? void 0 : _3.addressPincode) || ''}, ${((_4 = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic) === null || _4 === void 0 ? void 0 : _4.country) || ''}`
                };
                // Use the selectTemplate utility function to choose appropriate template
                const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.clinic, templateArgs, whatsapp_template_generator_1.getAppointmentCreatationTemplateData, whatsapp_template_generator_1.getAppointmentCreatationClinicLinkTemplateData);
                if ((0, get_login_url_1.isProductionOrUat)()) {
                    try {
                        this.whatsappService.sendTemplateMessage({
                            templateName,
                            valuesArray,
                            mobileNumber
                        });
                    }
                    catch (err) {
                        this.logger.error('error in sending whatsapp message', {
                            err
                        });
                    }
                }
            }
            if ((0, get_login_url_1.isProduction)() && ((_5 = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _5 === void 0 ? void 0 : _5.email)) {
                this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
        });
        return createdAppointment;
    }
    async syncOnAppointmentCreate(appointment, createdByUserId) {
        var _a, _b, _c;
        try {
            this.logger.debug('Starting Google Calendar sync for appointment', { appointmentId: appointment.id });
            // Reload appointment with relations so we have patient, clinic & doctor details for calendar description
            const appointmentWithRelations = await this.appointmentRepository.findOne({
                where: { id: appointment.id },
                relations: [
                    'patient',
                    'clinic',
                    'appointmentDoctors',
                    'appointmentDoctors.clinicUser',
                    'appointmentDoctors.clinicUser.user'
                ]
            });
            if (!appointmentWithRelations) {
                this.logger.warn(`Appointment ${appointment.id} not found when attempting Google Calendar sync.`);
                return;
            }
            let user = null;
            this.logger.debug('Loaded appointment with relations for Google Calendar sync', {
                appointmentId: appointment.id,
                doctorCount: ((_a = appointmentWithRelations.appointmentDoctors) === null || _a === void 0 ? void 0 : _a.length) || 0
            });
            // 1. Prefer the primary/first doctor attached to the appointment who has Google Calendar connected
            for (const doc of (_b = appointmentWithRelations.appointmentDoctors) !== null && _b !== void 0 ? _b : []) {
                const docUser = (_c = doc === null || doc === void 0 ? void 0 : doc.clinicUser) === null || _c === void 0 ? void 0 : _c.user;
                if (docUser &&
                    docUser.isGoogleSyncEnabled &&
                    docUser.googleCalendarRefreshToken &&
                    docUser.googleCalendarId) {
                    user = docUser;
                    break;
                }
            }
            // If no suitable Google-connected doctor found, we skip calendar sync
            if (!user) {
                return; // No eligible user
            }
            if (user &&
                user.isGoogleSyncEnabled &&
                user.googleCalendarRefreshToken &&
                user.googleCalendarId) {
                const googleEventId = await this.googleCalendarService.createEvent(user.id, appointmentWithRelations);
                if (googleEventId) {
                    await this.appointmentRepository.update(appointment.id, {
                        googleEventId
                    });
                }
            }
        }
        catch (error) {
            this.logger.error(`Error syncing appointment ${appointment.id}:`, {
                error: error.message,
                appointmentId: appointment.id,
                patientId: appointment.patientId
            });
        }
    }
    async getAllAppointments(page = 1, limit = 10, orderBy = 'DESC', date = '', search = '', doctors = [], status = [], onlyPrimary = false, clinicId = '', includeGoogleEvents = false, viewerUserId) {
        const startDate = new Date(date);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(date);
        endDate.setHours(23, 59, 59, 999);
        const query = this.appointmentRepository
            .createQueryBuilder('appointment')
            .leftJoinAndSelect('appointment.patient', 'patient')
            .leftJoinAndSelect('patient.patientOwners', 'patientOwner')
            .leftJoinAndSelect('patientOwner.ownerBrand', 'ownerBrand')
            .leftJoinAndSelect('ownerBrand.globalOwner', 'globalOwner')
            .leftJoinAndSelect('appointment.appointmentDoctors', 'appointmentDoctors')
            .leftJoinAndSelect('appointmentDoctors.clinicUser', 'clinicUser')
            .leftJoinAndSelect('clinicUser.user', 'doctor')
            .leftJoinAndSelect('appointment.room', 'room')
            .where('appointment.deletedAt IS NULL')
            .andWhere('appointment.clinicId = :clinicId', { clinicId })
            .andWhere('appointment.type != :impromptuType', {
            impromptuType: enum_appointment_type_1.EnumAppointmentType.Impromptu
        }); // Filter out impromptu appointments
        // .andWhere('appointment.date BETWEEN :startDate AND :endDate', {
        // 	startDate,
        // 	endDate
        // });
        if (date) {
            const startDate = new Date(date);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(date);
            endDate.setHours(23, 59, 59, 999);
            // this.logger.log('startDate, endDate', { startDate, endDate });
            // Add date filter if provided
            query.andWhere('appointment.date BETWEEN :startDate AND :endDate', {
                startDate,
                endDate
            });
        }
        if (onlyPrimary) {
            query.andWhere('appointmentDoctors.primary = :isPrimary', {
                isPrimary: true
            });
        }
        if (search && search.trim() !== '') {
            const tokens = search.split(/\s+/);
            tokens.forEach((token, index) => {
                const paramName = `token_${index}`;
                query.andWhere(new typeorm_2.Brackets(qb2 => {
                    qb2.where(`ownerBrand.firstName ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    })
                        .orWhere(`ownerBrand.lastName ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    })
                        .orWhere(`globalOwner.phoneNumber ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    })
                        .orWhere(`patient.patientName ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    })
                        .orWhere(`doctor.firstName ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    })
                        .orWhere(`doctor.lastName ILIKE :${paramName}`, {
                        [paramName]: `%${token}%`
                    });
                }));
            });
        }
        if (Array.isArray(doctors) && doctors.length > 0) {
            // Instead of filtering out non-matching doctors, use EXISTS to check if the appointment has any matching doctor
            query.andWhere(new typeorm_2.Brackets(qb => {
                qb.where(`EXISTS (
							SELECT 1
							FROM appointment_doctors ad
							WHERE ad.appointment_id = appointment.id
							AND ad.clinic_user_id IN (:...doctors)
						)`, { doctors });
            }));
        }
        if (Array.isArray(status) && status.length > 0) {
            query.andWhere('status IN (:...status)', {
                status
            });
        }
        query;
        query
            .addSelect(`TO_TIMESTAMP(
				TO_CHAR(appointment.date::date, 'YYYY-MM-DD') || ' ' || TO_CHAR(appointment.start_time::time, 'HH24:MI:SS'),
				'YYYY-MM-DD HH24:MI:SS'
			)`, 'sortable_timestamp' // Use a clear alias
        )
            .addOrderBy('sortable_timestamp', orderBy === 'ASC' ? 'ASC' : 'DESC')
            .skip((page - 1) * limit)
            .take(limit);
        const [appointments, total] = await query.getManyAndCount();
        const formattedAppointments = appointments
            .sort((appointment1, appointment2) => {
            const appointment1Date = moment(appointment1.date).format('YYYY-MM-DD');
            const appointment2Date = moment(appointment2.date).format('YYYY-MM-DD');
            const appointment1Time = moment(appointment1.startTime).format('HH:mm:ss');
            const appointment2Time = moment(appointment2.startTime).format('HH:mm:ss');
            const finalDate1 = moment(`${appointment1Date} ${appointment1Time}`, 'YYYY-MM-DD HH:mm:ss');
            const finalDate2 = moment(`${appointment2Date} ${appointment2Time}`, 'YYYY-MM-DD HH:mm:ss');
            if (finalDate1.isBefore(finalDate2)) {
                return -1;
            }
            if (finalDate1.isAfter(finalDate2)) {
                return 1;
            }
            return 0;
        })
            .map(appointment => {
            var _a;
            const formattedAppointment = {
                ...appointment,
                weight: (_a = appointment.weight) === null || _a === void 0 ? void 0 : _a.toString(),
                appointmentDoctors: appointment.appointmentDoctors.map(ad => ({
                    id: ad.id,
                    appointmentId: ad.appointmentId,
                    doctorId: ad.clinicUser.id,
                    primary: ad.primary,
                    doctor: {
                        id: ad.clinicUser.id,
                        firstName: ad.clinicUser.user.firstName,
                        lastName: ad.clinicUser.user.lastName,
                        email: ad.clinicUser.user.email
                    }
                }))
            };
            return formattedAppointment;
        });
        // If includeGoogleEvents is true, fetch and merge Google events
        let allAppointments = formattedAppointments;
        let totalCount = total;
        if (includeGoogleEvents) {
            try {
                this.logger.log(`[GoogleEvents] Fetching Google Calendar events for date: ${date}, clinic: ${clinicId}, doctors: ${JSON.stringify(doctors)}`);
                const googleEvents = await this.fetchGoogleEventsForDate(date, doctors, clinicId, viewerUserId);
                this.logger.log(`[GoogleEvents] Found ${googleEvents.length} Google Calendar events`);
                const existingGoogleEventKeys = new Set();
                formattedAppointments.forEach(appt => {
                    if (appt.googleEventId &&
                        Array.isArray(appt.appointmentDoctors)) {
                        appt.appointmentDoctors.forEach((doc) => {
                            existingGoogleEventKeys.add(`${appt.googleEventId}_${doc.doctorId}`);
                        });
                    }
                });
                // Exclude Google events that correspond to the same doctor & event already present in Nidana
                const filteredGoogleEvents = googleEvents.filter(ev => {
                    var _a;
                    if (!ev.googleEventId ||
                        !Array.isArray(ev.appointmentDoctors))
                        return true;
                    const doctorId = (_a = ev.appointmentDoctors[0]) === null || _a === void 0 ? void 0 : _a.doctorId;
                    if (!doctorId)
                        return true;
                    return !existingGoogleEventKeys.has(`${ev.googleEventId}_${doctorId}`);
                });
                allAppointments = [
                    ...formattedAppointments,
                    ...filteredGoogleEvents
                ];
                totalCount = total + filteredGoogleEvents.length;
                this.logger.log(`[GoogleEvents] Final result: ${formattedAppointments.length} Nidana appointments + ${filteredGoogleEvents.length} Google events = ${totalCount} total`);
            }
            catch (error) {
                this.logger.error(`Error fetching Google events:`, error);
                // Continue with just Nidana appointments if Google events fail
            }
        }
        this.logger.log('[AppointmentsService] getAllAppointments result', {
            date,
            googleEvents: totalCount - total,
            nidanaEvents: total,
            total: totalCount
        });
        return {
            appointments: allAppointments,
            total: totalCount
        };
    }
    async getAppointmentsForPatient(patientId, includeDeleted = false // Add optional parameter, default to false
    ) {
        const findOptions = {
            where: {
                patientId,
                type: (0, typeorm_2.Not)(enum_appointment_type_1.EnumAppointmentType.Impromptu) // Always filter out Impromptu type
            },
            relations: [
                'patient',
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user',
                'room',
                'appointmentDetails',
                'cart',
                'cart.invoice'
            ],
            order: { date: 'DESC', startTime: 'DESC' }
        };
        if (includeDeleted) {
            findOptions.withDeleted = true;
        }
        else {
            findOptions.where.deletedAt = (0, typeorm_2.IsNull)();
        }
        const appointments = await this.appointmentRepository.find(findOptions);
        if (includeDeleted) {
            appointments.forEach(appointment => {
                if (appointment.deletedAt) {
                    appointment.status = enum_appointment_status_1.EnumAppointmentStatus.Cancelled;
                }
            });
        }
        // Create missing appointment details for appointments that don't have them
        const appointmentsWithDetails = await Promise.all(appointments.map(async (appointment) => {
            // If appointment details don't exist, create them with default structure
            if (!appointment.appointmentDetails) {
                this.logger.log(`Creating missing appointment details for appointment ${appointment.id}`, {
                    appointmentId: appointment.id,
                    patientId
                });
                const defaultDetails = await this.generateDefaultTreatmentDetails(appointment.id, appointment.patientId);
                const createdAppointmentDetails = await this.appointmentDetailsRepository.save({
                    appointmentId: appointment.id,
                    details: defaultDetails
                });
                // Add the created appointment details to the appointment object
                appointment.appointmentDetails = createdAppointmentDetails;
            }
            return appointment;
        }));
        // Inject appointmentId into each appointment's details for the response
        const appointmentsWithInjectedDetails = await Promise.all(appointmentsWithDetails.map(appointment => this.injectAppointmentIdIntoDetails(appointment)));
        return appointmentsWithInjectedDetails;
    }
    /**
     * Helper method to inject appointmentId into appointment details
     * This adds appointmentId to the details object without saving it to the database
     * If appointment details are null, it provides default treatment details
     */
    async injectAppointmentIdIntoDetails(appointment) {
        var _a;
        // Check if appointment details exist and are not null
        if ((_a = appointment === null || appointment === void 0 ? void 0 : appointment.appointmentDetails) === null || _a === void 0 ? void 0 : _a.details) {
            // Create a copy of the details object and add appointmentId
            const detailsWithAppointmentId = {
                ...appointment.appointmentDetails.details,
                appointmentId: appointment.id
            };
            // Return the appointment with modified details
            return {
                ...appointment,
                appointmentDetails: {
                    ...appointment.appointmentDetails,
                    details: detailsWithAppointmentId
                }
            };
        }
        // If appointment details are null or missing, provide default details
        const defaultDetails = await this.generateDefaultTreatmentDetails(appointment.id, appointment.patientId);
        return {
            ...appointment,
            appointmentDetails: {
                id: null, // Will be null since no details record exists yet
                appointmentId: appointment.id,
                details: defaultDetails,
                createdAt: null,
                updatedAt: null
            }
        };
    }
    async getAppointmentDetails(appointmentId) {
        const appointmentDetail = await this.appointmentRepository
            .createQueryBuilder('appointment')
            .leftJoinAndSelect('appointment.appointmentDoctors', 'appointmentDoctors')
            .leftJoinAndSelect('appointmentDoctors.clinicUser', 'clinicUser')
            .leftJoinAndSelect('clinicUser.user', 'doctor')
            .leftJoinAndSelect('appointment.room', 'room')
            .leftJoinAndSelect('appointment.appointmentDetails', 'appointmentDetails')
            .where('appointment.id = :appointmentId', { appointmentId })
            .getOne();
        if (!appointmentDetail) {
            throw new common_1.NotFoundException(`Appointment details for appointment ID "${appointmentId}" not found`);
        }
        // If appointment details don't exist in the database, create them with default structure
        if (!appointmentDetail.appointmentDetails) {
            this.logger.log(`Creating missing appointment details for appointment ${appointmentId}`, {
                appointmentId
            });
            const defaultDetails = await this.generateDefaultTreatmentDetails(appointmentId, appointmentDetail.patientId);
            const createdAppointmentDetails = await this.appointmentDetailsRepository.save({
                appointmentId: appointmentId,
                details: defaultDetails
            });
            // Add the created appointment details to the appointment object
            appointmentDetail.appointmentDetails = createdAppointmentDetails;
        }
        // Restructure the appointmentDoctors to maintain the previous format
        const restructuredAppointment = {
            ...appointmentDetail,
            appointmentDoctors: appointmentDetail.appointmentDoctors.map(ad => ({
                id: ad.id,
                appointmentId: ad.appointmentId,
                doctorId: ad.clinicUser.id,
                primary: ad.primary,
                createdBy: ad.createdBy,
                updatedBy: ad.updatedBy,
                createdAt: ad.createdAt,
                updatedAt: ad.updatedAt,
                doctor: {
                    id: ad.clinicUser.id,
                    firstName: ad.clinicUser.user.firstName,
                    lastName: ad.clinicUser.user.lastName,
                    email: ad.clinicUser.user.email
                }
            }))
        };
        return restructuredAppointment;
    }
    async updateAppointmentDetails(id, updateAppointmentDetailsDto) {
        // First, find the appointment to check its status
        const appointment = await this.appointmentRepository.findOne({
            where: { id }
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment with ID "${id}" not found`);
        }
        // Validate that the appointment is in the Receiving Care state before allowing details update
        if (appointment.status === enum_appointment_status_1.EnumAppointmentStatus.Scheduled ||
            appointment.status === enum_appointment_status_1.EnumAppointmentStatus.Checkedin ||
            appointment.status === enum_appointment_status_1.EnumAppointmentStatus.Cancelled ||
            appointment.status === enum_appointment_status_1.EnumAppointmentStatus.Missed) {
            const currentStatus = appointment.status || enum_appointment_status_1.EnumAppointmentStatus.Scheduled;
            this.logger.warn(`Attempted to update details for appointment ${id} while not in Receiving Care state. Current state: ${currentStatus}`, { appointmentId: id, currentStatus });
        }
        let appointmentDetails = await this.appointmentDetailsRepository.findOne({
            where: { appointmentId: id }
        });
        // If appointment details don't exist, create them with default structure
        if (!appointmentDetails) {
            const defaultDetails = await this.generateDefaultTreatmentDetails(id, appointment.patientId);
            appointmentDetails = await this.appointmentDetailsRepository.save({
                appointmentId: id,
                details: defaultDetails
            });
        }
        // Filter out null values from the update DTO to prevent saving null data
        const filteredUpdateDto = this.filterNullValues(updateAppointmentDetailsDto);
        // Only proceed with update if there are non-null values to update
        if (Object.keys(filteredUpdateDto).length === 0) {
            this.logger.warn(`No valid data to update for appointment ${id}`, {
                appointmentId: id,
                originalDto: updateAppointmentDetailsDto
            });
            return { status: false };
        }
        Object.assign(appointmentDetails, filteredUpdateDto);
        const updateResult = await this.appointmentDetailsRepository.update(appointmentDetails.id, { details: appointmentDetails.details });
        if (updateResult.affected === 0) {
            return { status: false };
        }
        const updatedAppointment = await this.appointmentDetailsRepository.findOne({
            where: { appointmentId: id }
        });
        // Emit socket updates based on call site ID if update was successful
        if (updatedAppointment && updateAppointmentDetailsDto.callSiteId) {
            await this.emitSocketUpdatesForCallSite(id, updateAppointmentDetailsDto.callSiteId, updatedAppointment.details);
        }
        return updatedAppointment ? updatedAppointment : { status: false };
    }
    /**
     * Helper method to filter out null and undefined values from an object
     * This prevents saving null data to the database
     */
    filterNullValues(obj) {
        if (obj === null || obj === undefined) {
            return {};
        }
        const filtered = {};
        for (const [key, value] of Object.entries(obj)) {
            if (value !== null && value !== undefined) {
                // For nested objects, recursively filter null values
                if (typeof value === 'object' && !Array.isArray(value)) {
                    const filteredNested = this.filterNullValues(value);
                    if (Object.keys(filteredNested).length > 0) {
                        filtered[key] = filteredNested;
                    }
                }
                else {
                    filtered[key] = value;
                }
            }
        }
        return filtered;
    }
    async updateAppointmentStatus(id, updateAppointmentDto, invoiceId) {
        var _a, _b, _c;
        try {
            let appointment = await this.appointmentRepository.findOne({
                where: { id },
                relations: ['patient', 'appointmentDetails']
            });
            if (!appointment) {
                throw new common_1.NotFoundException(`Appointment with ID "${id}" not found`);
            }
            const currentStatus = appointment.status || enum_appointment_status_1.EnumAppointmentStatus.Scheduled;
            const newStatus = updateAppointmentDto.status || enum_appointment_status_1.EnumAppointmentStatus.Checkedin;
            if (!this.isValidStatusTransition(currentStatus, newStatus)) {
                throw new common_1.BadRequestException('Invalid status transition');
            }
            // Handle COMPLETED status with parallel EMR and reminder processing
            if (updateAppointmentDto.status === enum_appointment_status_1.EnumAppointmentStatus.Completed) {
                const appointmentDetails = await this.appointmentDetailsRepository.findOne({
                    where: { appointmentId: id }
                });
                // Create promises array only for reminders and EMR
                const completeStatusPromises = [];
                // this.sqsService.sendMessage({
                // 	queueKey: 'NidanaCreateEMR',
                // 	messageBody: {
                // 		traceID: TraceContext.getTraceId(),
                // 		data: {
                // 			appointmentId: id
                // 		}
                // 	},
                // 	deduplicationId: id
                // });
                // Add EMR creation promise
                // completeStatusPromises.push(
                // 	this.emrService.createEmr(id).catch(error => {
                // 		this.logger.error('Error creating EMR:', error);
                // 		return null;
                // 	})
                // );
                if (appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.details) {
                    const details = appointmentDetails.details;
                    // Extract plans
                    const plans = ((_a = details.plans) === null || _a === void 0 ? void 0 : _a.list) || [];
                    this.logger.log('Extracted plans', {
                        context: 'Appointments',
                        appointmentId: id,
                        plansCount: plans.length,
                        plans: plans.map((p) => ({
                            id: p.planId,
                            title: p.title
                        }))
                    });
                    // Extract prescriptions
                    const prescriptions = ((_b = details.prescription) === null || _b === void 0 ? void 0 : _b.list) || [];
                    this.logger.log('Extracted prescriptions', {
                        context: 'Appointments',
                        appointmentId: id,
                        prescriptionsCount: prescriptions.length,
                        prescriptions: prescriptions.map((p) => ({
                            id: p.prescriptionId,
                            name: p.name
                        }))
                    });
                    // Extract assessment list
                    const assessmentList = ((_c = details.assessment) === null || _c === void 0 ? void 0 : _c.list) || [];
                    this.logger.log('Extracted assessments', {
                        context: 'Appointments',
                        appointmentId: id,
                        assessmentsCount: assessmentList.length,
                        assessments: assessmentList.map((a) => ({
                            value: a.value,
                            label: a.label
                        }))
                    });
                    // Combine plans and prescriptions
                    const plansAndPrescriptions = [...plans, ...prescriptions];
                    console.log('Combined plans and prescriptions:', plansAndPrescriptions);
                    if (plansAndPrescriptions.length > 0) {
                        try {
                            // Get all pending reminders
                            const remindersResponse = await this.remindersService.findAll(appointment.patientId, reminder_enum_1.ReminderStatus.PENDING);
                            console.log('Found reminders:', remindersResponse);
                            const reminderUpdates = [];
                            // Process each plan/prescription
                            for (const item of plansAndPrescriptions) {
                                console.log('item Quantity', item.qty);
                                console.log('Processing item:', item);
                                // Find matching reminders
                                const matchingReminders = remindersResponse.reminders.filter(reminder => reminder.inventoryItemId ===
                                    item.prescriptionId ||
                                    reminder.inventoryItemId ===
                                        item.planId);
                                console.log('Matching reminders found:', matchingReminders);
                                // Process each matching reminder
                                for (const reminder of matchingReminders) {
                                    try {
                                        console.log('Completing reminder:', {
                                            reminderId: reminder.id,
                                            quantity: item.qty || 1,
                                            appointmentId: appointment === null || appointment === void 0 ? void 0 : appointment.id
                                        });
                                        const updatePromise = this.remindersService
                                            .completeReminder(reminder, 1, appointment === null || appointment === void 0 ? void 0 : appointment.id)
                                            .catch(error => {
                                            console.error('Error completing reminder:', {
                                                reminderId: reminder.id,
                                                error
                                            });
                                            this.logger.error('Error completing reminder', {
                                                reminderId: reminder.id,
                                                error
                                            });
                                            return null;
                                        });
                                        reminderUpdates.push(updatePromise);
                                    }
                                    catch (error) {
                                        console.error('Error processing reminder:', {
                                            reminderId: reminder.id,
                                            error
                                        });
                                    }
                                }
                            }
                            // Wait for all updates to complete
                            const results = await Promise.allSettled(reminderUpdates);
                            // Log results
                            const successful = results.filter(r => r.status === 'fulfilled').length;
                            const failed = results.filter(r => r.status === 'rejected').length;
                            this.logger.log('Reminder completion summary:', {
                                total: results.length,
                                successful,
                                failed
                            });
                            completeStatusPromises.push(Promise.resolve(results));
                        }
                        catch (error) {
                            console.error('Error processing reminders:', error);
                            this.logger.error('Error processing reminders:', error);
                        }
                    }
                    else {
                        console.log('No plans or prescriptions found');
                    }
                    // Process global reminders
                    try {
                        this.logger.log('Starting global reminders processing', {
                            context: 'Appointments',
                            appointmentId: id,
                            patientId: appointment.patientId,
                            clinicId: appointment.clinicId
                        });
                        await this.globalReminderService.processAppointmentTriggers(appointment.id, appointment.patientId, appointment.clinicId, appointment.brandId, plans, assessmentList, prescriptions, invoiceId);
                        this.logger.log('Global reminders processed successfully', {
                            context: 'Appointments',
                            appointmentId: id
                        });
                    }
                    catch (error) {
                        this.logger.error('Error processing global reminders:', {
                            context: 'Appointments',
                            error,
                            appointmentId: appointment.id
                        });
                    }
                }
                // Wait for EMR and reminders to complete
                await Promise.allSettled(completeStatusPromises);
            }
            // Handle SOAP pending task synchronously
            if (updateAppointmentDto.status ===
                enum_appointment_status_1.EnumAppointmentStatus.Checkedout &&
                updateAppointmentDto.soapPending) {
                try {
                    const createTaskDto = new create_task_dto_1.CreateTaskDto();
                    createTaskDto.isCompleted = false;
                    createTaskDto.title = `SOAP Pending for ${appointment.patient.patientName} who had an appointment on ${appointment.date.toDateString()}`;
                    createTaskDto.userId =
                        updateAppointmentDto.userId;
                    await this.tasksService.createTask(createTaskDto);
                }
                catch (error) {
                    this.logger.error('Error creating SOAP pending task:', error);
                    // Continue execution
                }
            }
            // Update appointment status and timestamps
            appointment.status = updateAppointmentDto.status;
            appointment = updateTimestamps(appointment, newStatus);
            // Save appointment status
            const updatedAppointment = await this.appointmentRepository.save(appointment);
            this.logger.log('Appointment status updated successfully', {
                appointmentId: id,
                newStatus: updateAppointmentDto.status
            });
            return updatedAppointment;
        }
        catch (error) {
            this.logger.error('Error in appointment status update', {
                appointmentId: id,
                error
            });
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update appointment status');
        }
    }
    async updateAppointment(id, updateAppointmentFieldsDto) {
        let appointment = await this.appointmentRepository.findOne({
            where: { id },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'clinic',
                'clinic.brand'
            ]
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment with ID "${id}" not found`);
        }
        // Store previous appointment data for availability update
        const previousAppointment = {
            date: appointment.date,
            startTime: appointment.startTime,
            endTime: appointment.endTime
        };
        await this.appointmentDoctorsRepository.delete({
            appointmentId: appointment.id
        });
        const doctorPromises = updateAppointmentFieldsDto.doctorIds.map(async (doctorId) => {
            await this.appointmentDoctorsRepository.save({
                appointmentId: appointment === null || appointment === void 0 ? void 0 : appointment.id,
                doctorId: doctorId,
                primary: true
            });
        });
        const providerPromises = updateAppointmentFieldsDto.providerIds.map(async (doctorId) => {
            await this.appointmentDoctorsRepository.save({
                appointmentId: appointment === null || appointment === void 0 ? void 0 : appointment.id,
                doctorId: doctorId,
                primary: false
            });
        });
        await Promise.all([...doctorPromises, ...providerPromises]);
        appointment = {
            ...appointment,
            ...updateAppointmentFieldsDto
        };
        if (updateAppointmentFieldsDto.status) {
            appointment = updateTimestamps(appointment, updateAppointmentFieldsDto.status);
        }
        const savedAppointment = await this.appointmentRepository.save(appointment);
        try {
            // Update appointment date to UTC
            appointment.date = this.convertToUTCDate(appointment.date);
            // Load complete appointment with required relations for availability update
            const appointmentWithDoctors = await this.appointmentRepository.findOne({
                where: { id: appointment.id },
                relations: [
                    'appointmentDoctors',
                    'appointmentDoctors.clinicUser'
                ]
            });
            if (!appointmentWithDoctors) {
                this.logger.error('Could not find appointment with relations for availability update', {
                    appointmentId: appointment.id
                });
                return savedAppointment;
            }
            appointmentWithDoctors.date = this.convertToUTCDate(appointmentWithDoctors.date);
            previousAppointment.date = this.convertToUTCDate(previousAppointment.date);
            await this.availabilityService.handleAppointmentChange(appointmentWithDoctors, 'update', previousAppointment);
        }
        catch (error) {
            this.logger.error('Error updating availability after updating appointment', {
                appointmentId: appointment.id,
                error
            });
        }
        // Sync with Google Calendar
        this.syncOnAppointmentUpdate(savedAppointment);
        try {
            // Reload full appointment with patient owners & clinic relations for notifications
            const appointmentWithRelations = await this.appointmentRepository.findOne({
                where: { id: savedAppointment.id },
                relations: [
                    'patient',
                    'patient.patientOwners',
                    'patient.patientOwners.ownerBrand',
                    'patient.patientOwners.ownerBrand.globalOwner',
                    'clinic',
                    'clinic.brand'
                ]
            });
            if (appointmentWithRelations) {
                await this.sendAppointmentNotifications(appointmentWithRelations, 'update');
            }
        }
        catch (notifyErr) {
            this.logger.error('Failed to send update notifications', {
                appointmentId: savedAppointment.id,
                notifyErr
            });
        }
        return savedAppointment;
    }
    /**
     * Synchronize appointment update with Google Calendar
     */
    async syncOnAppointmentUpdate(appointment) {
        var _a, _b;
        try {
            // Reload appointment with necessary relations (including doctors)
            const appointmentWithRelations = await this.appointmentRepository.findOne({
                where: { id: appointment.id },
                relations: [
                    'patient',
                    'clinic',
                    'appointmentDoctors',
                    'appointmentDoctors.clinicUser',
                    'appointmentDoctors.clinicUser.user'
                ]
            });
            if (!appointmentWithRelations) {
                return;
            }
            let user = null;
            // 1. Prefer a doctor attached to the appointment who has Google Calendar connected
            for (const doc of (_a = appointmentWithRelations.appointmentDoctors) !== null && _a !== void 0 ? _a : []) {
                const docUser = (_b = doc === null || doc === void 0 ? void 0 : doc.clinicUser) === null || _b === void 0 ? void 0 : _b.user;
                if (docUser &&
                    docUser.isGoogleSyncEnabled &&
                    docUser.googleCalendarRefreshToken &&
                    docUser.googleCalendarId) {
                    user = docUser;
                    break;
                }
            }
            // No doctor / updater user with Google Calendar – skip sync
            if (!user) {
                return;
            }
            if (user &&
                user.isGoogleSyncEnabled &&
                user.googleCalendarRefreshToken &&
                user.googleCalendarId) {
                this.logger.log(`Syncing updated appointment ${appointment.id} with Google Calendar for user ${user.id}`);
                const googleEventId = await this.googleCalendarService.updateEvent(user.id, appointmentWithRelations);
                // If an event was newly created during the update, persist the ID
                if (googleEventId && !appointment.googleEventId) {
                    await this.appointmentRepository.update(appointment.id, {
                        googleEventId
                    });
                }
            }
        }
        catch (error) {
            this.logger.error(`Failed to sync updated appointment ${appointment.id} with Google Calendar:`, error);
        }
    }
    /**
     * Fetch Google Calendar events for a specific date and convert them to appointment format
     */
    async fetchGoogleEventsForDate(date, doctors = [], clinicId, viewerUserId) {
        var _a, _b, _c, _d;
        // New implementation: query local cache instead of calling Google API per user
        try {
            if (!date)
                return [];
            // Normalize doctor filter array
            let parsedDoctors = Array.isArray(doctors)
                ? doctors
                : [];
            if (typeof doctors === 'string') {
                try {
                    parsedDoctors = JSON.parse(doctors);
                }
                catch (_e) {
                    parsedDoctors = [];
                }
            }
            // Get all users in clinic with sync enabled, apply doctor filter if any
            const usersQb = this.userRepository
                .createQueryBuilder('user')
                .innerJoin('user.clinicUsers', 'cu')
                .where('cu.clinicId = :clinicId', { clinicId })
                .andWhere('user.isGoogleSyncEnabled = true')
                .andWhere('user.googleCalendarId IS NOT NULL')
                .leftJoinAndSelect('user.clinicUsers', 'clinicUsers');
            if (parsedDoctors.length > 0) {
                usersQb.andWhere('cu.id IN (:...docs)', {
                    docs: parsedDoctors
                });
            }
            const users = await usersQb.getMany();
            this.logger.log(`[GoogleEvents] Found ${users.length} users with Google Calendar sync enabled in clinic ${clinicId}`);
            if (users.length === 0) {
                this.logger.warn(`[GoogleEvents] No users found with Google Calendar sync enabled for clinic ${clinicId}`);
                return [];
            }
            const startDate = new Date(date);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(date);
            endDate.setHours(23, 59, 59, 999);
            const googleEvents = [];
            const processedIds = new Set();
            for (const user of users) {
                const cached = await this.googleCalendarCache.getEventsRange(user.id, startDate, endDate);
                this.logger.log(`[GoogleEvents] User ${user.email} (${user.id}) has ${cached.length} cached events for ${date}`);
                for (const ev of cached) {
                    // Skip events that originated from Nidana (they carry a nidanaAppointmentId property)
                    if ((_c = (_b = (_a = ev.raw) === null || _a === void 0 ? void 0 : _a.extendedProperties) === null || _b === void 0 ? void 0 : _b.private) === null || _c === void 0 ? void 0 : _c.nidanaAppointmentId) {
                        continue;
                    }
                    if (ev.status === 'cancelled')
                        continue;
                    // Note: We intentionally skip the old deduplication check that
                    // filtered events purely by eventId so that the same Google event
                    // can be shown for multiple different users (doctors). Deduplication
                    // is now handled further below using a composite key (eventId + clinicUserId).
                    // Convert to appointment-like object
                    const clinicUser = (_d = user.clinicUsers) === null || _d === void 0 ? void 0 : _d.find(cu => cu.clinicId === clinicId);
                    if (!clinicUser)
                        continue;
                    // Create a composite key to allow the same Google eventId
                    // to appear for multiple different users (doctors).
                    const dedupKey = `${ev.eventId}_${clinicUser.id}`;
                    if (processedIds.has(dedupKey))
                        continue;
                    const isViewerOwner = viewerUserId && user.id === viewerUserId;
                    const safeSummary = isViewerOwner
                        ? ev.summary || 'Google Calendar Event'
                        : 'Busy';
                    const safeDescription = isViewerOwner
                        ? ev.description || ''
                        : '';
                    googleEvents.push({
                        id: `google_${dedupKey}`,
                        googleEventId: ev.eventId, // keep original eventId for reference
                        patientId: null,
                        clinicId,
                        date: ev.startTime,
                        startTime: ev.startTime,
                        endTime: ev.endTime,
                        status: ev.status || 'Scheduled',
                        type: 'Google Event',
                        reason: safeSummary,
                        notes: safeDescription,
                        weight: null,
                        triage: null,
                        deletedAt: null,
                        createdAt: ev.createdAt,
                        updatedAt: ev.updatedAt,
                        isBlocked: false,
                        brandId: null,
                        mode: 'Google',
                        patient: {
                            patientName: safeSummary,
                            patientOwners: []
                        },
                        appointmentDoctors: [
                            {
                                id: null,
                                appointmentId: null,
                                doctorId: clinicUser.id,
                                primary: true,
                                doctor: {
                                    id: clinicUser.id,
                                    firstName: user.firstName,
                                    lastName: user.lastName,
                                    email: user.email
                                }
                            }
                        ]
                    });
                    processedIds.add(dedupKey);
                }
            }
            return googleEvents;
        }
        catch (error) {
            this.logger.error('Failed to fetch cached Google events:', error);
            return [];
        }
    }
    /**
     * Convert a Google Calendar event to appointment format
     */
    convertGoogleEventToAppointment(event, user, clinicId, viewerUserId) {
        var _a, _b, _c;
        try {
            // Ignore cancelled events
            if (event.status === 'cancelled') {
                return null;
            }
            if (!((_a = event.start) === null || _a === void 0 ? void 0 : _a.dateTime) || !((_b = event.end) === null || _b === void 0 ? void 0 : _b.dateTime)) {
                return null;
            }
            const startTime = new Date(event.start.dateTime);
            const endTime = new Date(event.end.dateTime);
            // Find the correct clinic user for this clinic
            const clinicUser = (_c = user.clinicUsers) === null || _c === void 0 ? void 0 : _c.find((cu) => cu.clinicId === clinicId);
            // If the user is not mapped to this clinic, skip showing this event to prevent it
            // from appearing under an unrelated doctor's schedule.
            if (!clinicUser) {
                return null;
            }
            const clinicUserId = clinicUser.id;
            // Determine visibility of event details
            const isViewerOwner = viewerUserId && user.id === viewerUserId;
            const safeSummary = isViewerOwner
                ? event.summary || 'Google Calendar Event'
                : 'Busy';
            const safeDescription = isViewerOwner
                ? event.description || ''
                : '';
            const convertedAppointment = {
                id: `google_${event.id}`,
                googleEventId: event.id,
                patientId: null,
                clinicId: clinicId,
                date: startTime,
                startTime: startTime,
                endTime: endTime,
                status: event.status === 'cancelled' ? 'Cancelled' : 'Scheduled',
                type: 'Google Event',
                reason: safeSummary,
                notes: safeDescription,
                weight: null,
                triage: null,
                deletedAt: null,
                createdAt: new Date(event.created || event.updated),
                updatedAt: new Date(event.updated),
                checkinTime: null,
                receivingCareTime: null,
                checkoutTime: null,
                isBlocked: false,
                brandId: null,
                mode: 'Google',
                patient: {
                    id: null,
                    patientName: safeSummary,
                    patientOwners: [
                        {
                            ownerBrand: {
                                firstName: 'Google',
                                lastName: 'Calendar',
                                globalOwner: {
                                    phoneNumber: ''
                                }
                            }
                        }
                    ]
                },
                appointmentDoctors: [
                    {
                        id: null,
                        appointmentId: null,
                        doctorId: clinicUserId,
                        primary: true,
                        doctor: {
                            id: clinicUserId,
                            firstName: user.firstName,
                            lastName: user.lastName,
                            email: user.email
                        }
                    }
                ],
                room: null
            };
            return convertedAppointment;
        }
        catch (error) {
            this.logger.error(`Failed to convert Google event ${event.id} to appointment format:`, error);
            return null;
        }
    }
    isValidStatusTransition(currentStatus, newStatus) {
        // Define valid transitions
        const validTransitions = {
            [enum_appointment_status_1.EnumAppointmentStatus.Scheduled]: [
                enum_appointment_status_1.EnumAppointmentStatus.Checkedin,
                enum_appointment_status_1.EnumAppointmentStatus.Cancelled
            ],
            [enum_appointment_status_1.EnumAppointmentStatus.Checkedin]: [
                enum_appointment_status_1.EnumAppointmentStatus.ReceivingCare,
                enum_appointment_status_1.EnumAppointmentStatus.Cancelled
            ],
            [enum_appointment_status_1.EnumAppointmentStatus.ReceivingCare]: [
                enum_appointment_status_1.EnumAppointmentStatus.Checkedout,
                enum_appointment_status_1.EnumAppointmentStatus.Cancelled
            ],
            [enum_appointment_status_1.EnumAppointmentStatus.Checkedout]: [
                enum_appointment_status_1.EnumAppointmentStatus.Completed,
                enum_appointment_status_1.EnumAppointmentStatus.Cancelled
            ],
            [enum_appointment_status_1.EnumAppointmentStatus.Completed]: [],
            [enum_appointment_status_1.EnumAppointmentStatus.Cancelled]: []
        };
        const allowedStatuses = validTransitions[currentStatus];
        return allowedStatuses ? allowedStatuses.includes(newStatus) : false;
    }
    async deleteAppointment(id) {
        var _a, _b;
        const appointment = await this.appointmentRepository.findOne({
            where: { id },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user',
                'clinic',
                'clinic.brand'
            ]
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`This appointment with ${id} doesn't exist`);
        }
        // Sync with Google Calendar before deleting - use appointment doctors like create/update
        if (appointment.googleEventId) {
            // Find a doctor attached to the appointment who has Google Calendar connected
            let user = null;
            for (const doc of (_a = appointment.appointmentDoctors) !== null && _a !== void 0 ? _a : []) {
                const docUser = (_b = doc === null || doc === void 0 ? void 0 : doc.clinicUser) === null || _b === void 0 ? void 0 : _b.user;
                if (docUser &&
                    docUser.isGoogleSyncEnabled &&
                    docUser.googleCalendarRefreshToken &&
                    docUser.googleCalendarId) {
                    user = docUser;
                    break;
                }
            }
            if (user) {
                await this.googleCalendarService.deleteEvent(user.id, appointment.googleEventId);
            }
        }
        appointment.deletedAt = new Date();
        const updatedAppointment = await this.appointmentRepository.update(id, {
            deletedAt: appointment.deletedAt
        });
        // Handle availability
        try {
            // Extract local date parts to preserve the correct calendar date
            const localDate = new Date(appointment.date);
            const year = localDate.getFullYear();
            const month = localDate.getMonth();
            const day = localDate.getDate();
            // Create UTC date to preserve the exact calendar date
            appointment.date = new Date(Date.UTC(year, month, day));
            await this.availabilityService.handleAppointmentChange(appointment, 'delete');
        }
        catch (error) {
            this.logger.error('Error updating availability after deleting appointment', {
                appointmentId: id,
                error
            });
            // Continue with deletion even if availability update fails
        }
        // Send cancellation notifications
        try {
            await this.sendAppointmentNotifications(appointment, 'cancel');
        }
        catch (notifyErr) {
            this.logger.error('Failed to send cancellation notifications', {
                appointmentId: id,
                notifyErr
            });
        }
        return { status: true };
    }
    // @Cron(CronExpression.EVERY_HOUR)
    // async sendAppointmentMailBy24thHour() {
    // 	const lockKey = 'upcoming_appointment_reminder_cron_lock';
    // 	const lock = await redisClient.set(lockKey, 'locked', 'EX', 60*55 , 'NX');
    // 	if (!lock) {
    // 	  this.logger.log('Cron job already running on another instance. Skipping execution.');
    // 	  return;
    // 	}
    // 	this.logger.log('sendAppointmentMailBy24thHour service is called');
    // 	const appintmentStartTime = moment()
    // 		.add(16, 'hours')
    // 		.startOf('hour')
    // 		.format('YYYY-MM-DD HH:mm:ss');
    // 	const appintmentEndTime = moment()
    // 		.add(16, 'hours')
    // 		.endOf('hour')
    // 		.format('YYYY-MM-DD HH:mm:ss');
    // 	const appointments: AppointmentEntity[] =
    // 		await this.appointmentRepository.find({
    // 			where: {
    // 				startTime: Between(
    // 					new Date(appintmentStartTime),
    // 					new Date(appintmentEndTime)
    // 				)
    // 			},
    // 			relations: [
    // 				'appointmentDoctors',
    // 				'appointmentDoctors.clinicUser',
    // 				'appointmentDoctors.clinicUser.user',
    // 				'patient.patientOwners.ownerBrand',
    // 				'patient.patientOwners.ownerBrand.globalOwner',
    // 				'clinic',
    // 				'clinic.brand'
    // 			]
    // 		});
    // 	this.logger.log(
    // 		'sendAppointmentMailBy24thHour :>> appointmentRepository.find is called'
    // 	);
    // 	appointments.forEach(appointment => {
    // 		appointment?.patient?.patientOwners.forEach((patientOwner: any) => {
    // 			// Use ownerBrand branch for appointment reminders
    // 			const ownerFirstName =
    // 				patientOwner?.ownerBrand?.firstName || '';
    // 			const ownerLastName = patientOwner?.ownerBrand?.lastName || '';
    // 			const ownerMobileNumber = `${patientOwner?.ownerBrand?.globalOwner?.countryCode || ''}${patientOwner?.ownerBrand?.globalOwner?.phoneNumber || ''}`;
    // 			if (patientOwner?.ownerBrand?.email) {
    // 				const { body, subject, toMailAddress } =
    // 					appointmentReminderMailGenerator({
    // 						brandName: appointment?.clinic?.brand?.name,
    // 						contactInformation:
    // 							appointment?.clinic?.phoneNumbers?.[0]
    // 								?.number || 'provided contact no.',
    // 						petName: appointment?.patient?.patientName,
    // 						email: patientOwner?.ownerBrand?.email,
    // 						firstname: ownerFirstName,
    // 						lastName: ownerLastName,
    // 						appointmentdate: moment(appointment.date).format(
    // 							'MMMM Do YYYY'
    // 						),
    // 						appointmentTime: `${moment(appointment.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')} - ${moment(appointment.endTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`
    // 					});
    // 				if (isProduction()) {
    // 					this.mailService.sendMail({
    // 						body,
    // 						subject,
    // 						toMailAddress
    // 					});
    // 				} else if (!isProduction()) {
    // 					this.mailService.sendMail({
    // 						body,
    // 						subject,
    // 						toMailAddress: DEV_SES_EMAIL //appointment.patient.patientOwners[0].owner.email
    // 					});
    // 				}
    // 			}
    // 			if (ownerMobileNumber) {
    // 				const { mobileNumber, templateName, valuesArray } =
    // 					getAppointmentReminderTemplateData({
    // 						appointmentDate: moment(appointment?.date).format(
    // 							'MMMM Do YYYY'
    // 						),
    // 						appointmentTime: `${moment(appointment?.startTime).add(5, 'hours').add(30, 'minute').format('h:mm a')} - ${moment(appointment.endTime).add(5, 'hours').add(30, 'minute').format('h:mm a')}`,
    // 						brandName: appointment?.clinic?.brand?.name,
    // 						contactInformation:
    // 							appointment?.clinic?.phoneNumbers?.[0]
    // 								?.number || '',
    // 						clientName: `${ownerFirstName} ${ownerLastName}`,
    // 						mobileNumber: ownerMobileNumber,
    // 						petName: appointment.patient.patientName
    // 					});
    // 				if (isProductionOrUat()) {
    // 					try {
    // 						this.whatsappService.sendTemplateMessage({
    // 							templateName,
    // 							valuesArray,
    // 							mobileNumber
    // 						});
    // 					} catch (err) {
    // 						console.log('error in sending whatsapp message', {
    // 							err
    // 						});
    // 						this.logger.error(
    // 							'error in sending whatsapp message',
    // 							{
    // 								err
    // 							}
    // 						);
    // 					}
    // 				}
    // 			}
    // 		});
    // 		this.logger.log('appoitmentMailBy24thHour mail service is called');
    // 	});
    // }
    async checkPatientOnGoingAppointment(patientId) {
        const appointment = await this.appointmentRepository.findOne({
            where: {
                patientId,
                status: (0, typeorm_2.In)([
                    enum_appointment_status_1.EnumAppointmentStatus.Checkedin,
                    enum_appointment_status_1.EnumAppointmentStatus.ReceivingCare,
                    enum_appointment_status_1.EnumAppointmentStatus.Checkedout
                ])
                //date: new Date()
            }
        });
        if (!appointment) {
            return { hasOngoingAppointment: false, appointment: null };
        }
        return { hasOngoingAppointment: true, appointment };
    }
    async downloadTodaysAppointment(clinicId, date) {
        this.logger.log('service called for downloading todays appointmentList', date);
        const customDate = moment(date).add(5, 'hours').add(30, 'minutes');
        const limit = 100;
        try {
            const response = await this.getAllAppointments(1, limit, 'DESC', customDate.toString(), '', [], [], false, clinicId);
            this.logger.log('appointmentResponse', response);
            if (response && response.appointments.length > 0) {
                const appointmentDetailsList = response === null || response === void 0 ? void 0 : response.appointments.map(list => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x;
                    let patientAge = '';
                    if ((_a = list === null || list === void 0 ? void 0 : list.patient) === null || _a === void 0 ? void 0 : _a.age) {
                        const age = (0, invoice_service_1.calculateAge)((_b = list === null || list === void 0 ? void 0 : list.patient) === null || _b === void 0 ? void 0 : _b.age);
                        patientAge = `${age} yrs`;
                    }
                    return {
                        timeStart: moment(list === null || list === void 0 ? void 0 : list.startTime)
                            .add(5, 'hours')
                            .add(30, 'minutes')
                            .format('LT') || '',
                        timeEnd: moment(list === null || list === void 0 ? void 0 : list.endTime)
                            .add(5, 'hours')
                            .add(30, 'minutes')
                            .format('LT') || '',
                        petName: ((_c = list === null || list === void 0 ? void 0 : list.patient) === null || _c === void 0 ? void 0 : _c.patientName) || '',
                        petBreed: ((_d = list === null || list === void 0 ? void 0 : list.patient) === null || _d === void 0 ? void 0 : _d.breed) || '',
                        petAge: patientAge || '',
                        ownerName: `${(_g = (_f = (_e = list === null || list === void 0 ? void 0 : list.patient) === null || _e === void 0 ? void 0 : _e.patientOwners[0]) === null || _f === void 0 ? void 0 : _f.ownerBrand) === null || _g === void 0 ? void 0 : _g.firstName} ${(_k = (_j = (_h = list === null || list === void 0 ? void 0 : list.patient) === null || _h === void 0 ? void 0 : _h.patientOwners[0]) === null || _j === void 0 ? void 0 : _j.ownerBrand) === null || _k === void 0 ? void 0 : _k.lastName}`,
                        ownerPhone: `+${((_p = (_o = (_m = (_l = list === null || list === void 0 ? void 0 : list.patient) === null || _l === void 0 ? void 0 : _l.patientOwners[0]) === null || _m === void 0 ? void 0 : _m.ownerBrand) === null || _o === void 0 ? void 0 : _o.globalOwner) === null || _p === void 0 ? void 0 : _p.countryCode) || ''} ${((_t = (_s = (_r = (_q = list === null || list === void 0 ? void 0 : list.patient) === null || _q === void 0 ? void 0 : _q.patientOwners[0]) === null || _r === void 0 ? void 0 : _r.ownerBrand) === null || _s === void 0 ? void 0 : _s.globalOwner) === null || _t === void 0 ? void 0 : _t.phoneNumber) || ''}`,
                        doctorName: `Dr. ${((_u = list === null || list === void 0 ? void 0 : list.appointmentDoctors[0]) === null || _u === void 0 ? void 0 : _u.doctor.firstName) || ''} ${(_w = (_v = list === null || list === void 0 ? void 0 : list.appointmentDoctors[0]) === null || _v === void 0 ? void 0 : _v.doctor) === null || _w === void 0 ? void 0 : _w.lastName}`,
                        room: ((_x = list === null || list === void 0 ? void 0 : list.room) === null || _x === void 0 ? void 0 : _x.name) || '',
                        visitType: (list === null || list === void 0 ? void 0 : list.type) || '',
                        treatment: (list === null || list === void 0 ? void 0 : list.reason) || ''
                    };
                });
                this.logger.log('latest appointmentList', appointmentDetailsList);
                const data = {
                    appointments: appointmentDetailsList,
                    date: moment(date).format('DD/MM/YYYY')
                };
                const html = (0, todays_appoitments_1.generateAppointmentSummary)(data === null || data === void 0 ? void 0 : data.date, data.appointments);
                const pdfBuffer = await (0, generatePdf_1.generatePDF)(html);
                const outputPath = path.resolve('output.pdf');
                await fs.writeFile(outputPath, pdfBuffer);
                this.logger.log('document created succesfully');
                return outputPath;
            }
            else {
                this.logger.log('No appointments found for today');
                throw new Error('No appointments found.');
            }
        }
        catch (error) {
            this.logger.error('Error fetching appointments for PDF generation', { error });
            throw error;
        }
    }
    /**
     * Cron job that runs every hour to check for missed appointments
     * Finds appointments that are:
     * 1. Not in Completed, Cancelled, or Missed status
     * 2. More than 16 hours old (based on combined date and start time)
     * And marks them as Missed
     */
    async handleMissedAppointments() {
        try {
            this.logger.log('Starting missed appointments check');
            // Calculate the cutoff time (16 hours ago)
            const cutoffTime = moment().subtract(16, 'hours').toDate();
            // Find appointments that meet our criteria using combined date and time
            const missedAppointments = await this.appointmentRepository
                .createQueryBuilder('appointment')
                .where('appointment.deletedAt IS NULL')
                .andWhere(`make_timestamp(
						EXTRACT(YEAR FROM appointment.date)::int,
						EXTRACT(MONTH FROM appointment.date)::int,
						EXTRACT(DAY FROM appointment.date)::int,
						EXTRACT(HOUR FROM appointment.start_time)::int,
						EXTRACT(MINUTE FROM appointment.start_time)::int,
						EXTRACT(SECOND FROM appointment.start_time)::double precision
					) < :cutoffTime`, { cutoffTime })
                .andWhere('appointment.status IN (:...statuses)', {
                statuses: [enum_appointment_status_1.EnumAppointmentStatus.Scheduled]
            })
                .getMany();
            if (missedAppointments.length === 0) {
                this.logger.log('No missed appointments found');
                return;
            }
            this.logger.log(`Found ${missedAppointments.length} missed appointments`);
            // Update all found appointments to Missed status
            const updatePromises = missedAppointments.map(async (appointment) => {
                try {
                    appointment.status = enum_appointment_status_1.EnumAppointmentStatus.Missed;
                    await this.appointmentRepository.save(appointment);
                    this.logger.log(`Updated appointment ${appointment.id} to Missed status`);
                }
                catch (error) {
                    this.logger.error(`Failed to update appointment ${appointment.id}`, error);
                }
            });
            await Promise.all(updatePromises);
            this.logger.log(`Successfully processed ${missedAppointments.length} missed appointments`);
        }
        catch (error) {
            this.logger.error('Error in handleMissedAppointments cron job', error);
        }
    }
    async createImpromptuAppointment(data) {
        // Create appointment record
        const appointment = this.appointmentRepository.create({
            ...data
        });
        const createdAppointment = await this.appointmentRepository.save(appointment);
        // Create appointment details record with default JSONB structure
        const defaultDetails = {
            plans: { list: [], notes: '' },
            followup: null,
            objective: {
                vitals: [],
                bodyMaps: [],
                labReports: [],
                physicalExam: [
                    {
                        id: '05bb1078-ea01-407b-bad7-6933dc85b196',
                        notes: '',
                        status: '',
                        category: 'Oral cavity/Teeth'
                    },
                    {
                        id: 'f7091c98-0e83-47af-89d0-8a74959ac555',
                        notes: '',
                        status: '',
                        category: 'Eyes/orbit'
                    },
                    {
                        id: 'c4fcdff2-4f5b-4743-bbb8-fd92416dbbf6',
                        notes: '',
                        status: '',
                        category: 'Throat'
                    },
                    {
                        id: '60f0b78f-44c4-4368-80a1-587f3196eec5',
                        notes: '',
                        status: '',
                        category: 'Respiratory'
                    },
                    {
                        id: '78ae866b-ea47-4fed-986a-71058afde0b3',
                        notes: '',
                        status: '',
                        category: 'Musculoskeletal'
                    },
                    {
                        id: 'b6a2d620-6d89-4447-89fc-668f23c19906',
                        notes: '',
                        status: '',
                        category: 'Urogenital'
                    },
                    {
                        id: '64c8facb-54b2-4510-986a-5f8791f5a88a',
                        notes: '',
                        status: '',
                        category: 'Mucous menbranes'
                    },
                    {
                        id: '3d2989b0-8692-422b-bb65-9a946117cee4',
                        notes: '',
                        status: '',
                        category: 'Ears'
                    },
                    {
                        id: '3f599bd8-6c58-45a8-88a1-29d2ac606dd3',
                        notes: '',
                        status: '',
                        category: 'Cardio vascular'
                    },
                    {
                        id: 'c268247e-ab34-407b-8837-aef7fd2bef6c',
                        notes: '',
                        status: '',
                        category: 'Abdomen'
                    },
                    {
                        id: '8cadee67-8af4-4c0f-bcaf-5191e0fc5a07',
                        notes: '',
                        status: '',
                        category: 'Glands/Lymph Nodes'
                    },
                    {
                        id: 'ad562754-fd3e-413e-8f7c-add2dd3ff3ae',
                        notes: '',
                        status: '',
                        category: 'Rectal'
                    }
                ],
                ultrasoundExam: [
                    {
                        id: '59452a26-dd76-482f-823e-8910e30463e3',
                        notes: 'The liver is regular in size with a homogenous echo pattern throughout the parenchyma.\n        The liver margins are well-defined and smooth.\n        No focal lesion or intrahepatic duct dilatation evident.',
                        status: '',
                        category: 'Liver'
                    },
                    {
                        id: 'a94daca9-b5cc-4c0b-8388-c0849ce80955',
                        notes: 'The portal vein is patent, demonstrates hepatopetal flow with a velocity of **___ cm/s**.\n        Hepatic veins demonstrate normal calibre and regular configuration.',
                        status: '',
                        category: 'Portal Vein'
                    },
                    {
                        id: 'a449583d-d6af-4e5b-9d1c-78c23dc3573a',
                        notes: 'Normal distended gallbladder with a uniform thin wall.\n        Sludge/Choleliths not seen within the dependent portion of the gallbladder.\n        The common duct measurement was normal at the level of the main portal branch.',
                        status: '',
                        category: 'Gallbladder'
                    },
                    {
                        id: 'd79be8b6-437e-4225-b497-80370c4da79a',
                        notes: 'Regular sized and shaped spleen with a normal homogenous parenchymal echo pattern.\n        No evidence of splenic disease.',
                        status: '',
                        category: 'Spleen'
                    },
                    {
                        id: '5f14af57-fde1-4225-a70a-e13ee66c84cf',
                        notes: 'Where visualised, the pancreas is normal in size and echotexture with no evidence of pancreatic disease.',
                        status: '',
                        category: 'Pancreas'
                    },
                    {
                        id: 'd9238510-38ce-4254-af3d-fa3e65732496',
                        notes: 'The stomach is empty at the time of the scan.\n        Normal wall layer architecture and gastric wall thickness (**___ mm**).\n        No obvious foreign body seen within the contracted rugal folds.',
                        status: '',
                        category: 'Stomach'
                    },
                    {
                        id: '9d83587b-61d6-47ce-8c78-a33fd897009c',
                        notes: 'Duodenum demonstrates normal overall wall thickness (**___ mm**) and characteristic wall layer echo pattern.\n        Remaining small intestines are normal with no evidence of foreign body or disease.',
                        status: '',
                        category: 'Small Intestines'
                    },
                    {
                        id: 'e2f43078-4fe1-461d-93b9-a482bc6162d8',
                        notes: 'The colon contains air/faecal content/fluid at the time of scan.\n        Wall layering and overall thickness appear normal.\n        ICCJ identified as normal.',
                        status: '',
                        category: 'Colon'
                    },
                    {
                        id: '57a25fd0-7c6d-4a3b-9d6a-259046fcefff',
                        notes: 'Both kidneys are normal in size, shape, and \n        echogenicity with cortico-medullary differentiation. \n        There is no evidence of pyelectasia or calculi. \n        Left kidney: **___ mm**, Right kidney: **___ mm** (long axis).',
                        status: '',
                        category: 'Kidneys'
                    },
                    {
                        id: '94fe8ba4-d284-4f07-9ba8-e93a4306a775',
                        notes: 'Normal in size, echogenicity, and characteristic shape\n        Left adrenal: **___ mm**, Right adrenal: **___ mm** (caudal pole).',
                        status: '',
                        category: 'Adrenal Glands'
                    },
                    {
                        id: 'ac2bc911-d247-45bd-84b5-c1d1667062ba',
                        notes: 'No evidence of dilation or thrombus noted.',
                        status: '',
                        category: 'Aorta'
                    },
                    {
                        id: '27cc51cb-d247-4f53-ae08-2cbe6ec8f77c',
                        notes: 'No evidence of thrombus or other abnormalities.',
                        status: '',
                        category: 'Caudal Vena Cava (CVC)'
                    },
                    {
                        id: '7f4409c2-b0b2-4c85-947f-b4f8c0463ba5',
                        notes: 'Moderately filled at time of scan.\n        Smooth, uniform wall thickness within normal limits for fill status (**___ mm**).\n        Trigone region did not show any abnormalities.',
                        status: '',
                        category: 'Urinary Bladder'
                    },
                    {
                        id: '073efb8b-bdb1-47c4-897e-8e94a88d7e63',
                        notes: 'Prostate gland is normal in size, shape, and echotexture\n        Bilobar measurements: **___ mm x ___ mm**.\n        Prostate volume: **___ ml\n        Testes: Normal shape, size, and echotexture (if intact).\n        ',
                        status: '',
                        category: 'Prostate Gland / Testes'
                    },
                    {
                        id: '01d2bf00-36e8-4041-b1b4-349d6e1014fb',
                        notes: '**Neutered/Intact status:** _______________.\n        Uterus and ovaries could not be visualized.',
                        status: '',
                        category: 'Uterus / Ovaries'
                    },
                    {
                        id: '7ece491d-80d7-4b98-94b7-dca7e3f32e69',
                        notes: 'LMILN: **___ x ___ mm**, RMILN: **___ x ___ mm**.\n        No gross lymphadenopathy.',
                        status: '',
                        category: 'Lymph Nodes'
                    },
                    {
                        id: '3fb01669-b03a-4c33-a32a-35182c2ca47b',
                        notes: 'No free abdominal fluid demonstrated.',
                        status: '',
                        category: 'Peritoneum'
                    }
                ]
            },
            assessment: { list: [], notes: '' },
            subjective: '',
            attachments: { list: [] },
            prescription: { list: [], notes: '' },
            invoiceAmount: 0
        };
        await this.appointmentDetailsRepository.save({
            appointmentId: createdAppointment.id,
            details: defaultDetails
        });
        this.logger.log('Created impromptu appointment', {
            appointmentId: createdAppointment.id,
            type: data.type,
            status: data.status
        });
        return createdAppointment;
    }
    /**
     * Generate default treatment details structure for new appointments
     * This initializes the appointment details with the standard SOAP format
     * and includes long-term medications in the prescription list
     */
    async generateDefaultTreatmentDetails(appointmentId, patientId) {
        // Physical exam categories - basic structure
        const physicalExamCategories = [
            'Oral cavity/Teeth',
            'Eyes/orbit',
            'Throat',
            'Respiratory',
            'Musculoskeletal',
            'Urogenital',
            'Mucous menbranes',
            'Ears',
            'Cardio vascular',
            'Abdomen',
            'Glands/Lymph Nodes',
            'Rectal'
        ];
        const ultrasoundExamCategories = [
            {
                category: 'Liver',
                notes: `The liver is regular in size with a homogenous echo pattern throughout the parenchyma.
				The liver margins are well-defined and smooth.
				No focal lesion or intrahepatic duct dilatation evident.`
            },
            {
                category: 'Portal Vein',
                notes: `The portal vein is patent, demonstrates hepatopetal flow with a velocity of **___ cm/s**.
				Hepatic veins demonstrate normal calibre and regular configuration.`
            },
            {
                category: 'Gallbladder',
                notes: `Normal distended gallbladder with a uniform thin wall.
				Sludge/Choleliths not seen within the dependent portion of the gallbladder.
				The common duct measurement was normal at the level of the main portal branch.`
            },
            {
                category: 'Spleen',
                notes: `Regular sized and shaped spleen with a normal homogenous parenchymal echo pattern.
				No evidence of splenic disease.`
            },
            {
                category: 'Pancreas',
                notes: `Where visualised, the pancreas is normal in size and echotexture with no evidence of pancreatic disease.`
            },
            {
                category: 'Stomach',
                notes: `The stomach is empty at the time of the scan.
				Normal wall layer architecture and gastric wall thickness (**___ mm**).
				No obvious foreign body seen within the contracted rugal folds.`
            },
            {
                category: 'Small Intestines',
                notes: `Duodenum demonstrates normal overall wall thickness (**___ mm**) and characteristic wall layer echo pattern.
				Remaining small intestines are normal with no evidence of foreign body or disease.`
            },
            {
                category: 'Colon',
                notes: `The colon contains air/faecal content/fluid at the time of scan.
				Wall layering and overall thickness appear normal.
				ICCJ identified as normal.`
            },
            {
                category: 'Kidneys',
                notes: `Both kidneys are normal in size, shape, and 
				echogenicity with cortico-medullary differentiation. 
				There is no evidence of pyelectasia or calculi. 
				Left kidney: **___ mm**, Right kidney: **___ mm** (long axis).`
            },
            {
                category: 'Adrenal Glands',
                notes: `Normal in size, echogenicity, and characteristic shape
				Left adrenal: **___ mm**, Right adrenal: **___ mm** (caudal pole).`
            },
            {
                category: 'Aorta',
                notes: `No evidence of dilation or thrombus noted.`
            },
            {
                category: 'Caudal Vena Cava (CVC)',
                notes: `No evidence of thrombus or other abnormalities.`
            },
            {
                category: 'Urinary Bladder',
                notes: `Moderately filled at time of scan.
				Smooth, uniform wall thickness within normal limits for fill status (**___ mm**).
				Trigone region did not show any abnormalities.`
            },
            {
                category: 'Prostate Gland / Testes',
                notes: `Prostate gland is normal in size, shape, and echotexture
				Bilobar measurements: **___ mm x ___ mm**.
				Prostate volume: **___ ml
				Testes: Normal shape, size, and echotexture (if intact).
				`
            },
            {
                category: 'Uterus / Ovaries',
                notes: `**Neutered/Intact status:** _______________.
				Uterus and ovaries could not be visualized.`
            },
            {
                category: 'Lymph Nodes',
                notes: `LMILN: **___ x ___ mm**, RMILN: **___ x ___ mm**.
				No gross lymphadenopathy.`
            },
            {
                category: 'Peritoneum',
                notes: `No free abdominal fluid demonstrated.`
            }
        ];
        // Fetch long-term medications for the patient if patientId is provided
        let longTermMedications = [];
        if (patientId) {
            try {
                longTermMedications =
                    await this.longTermMedicationsService.getLongTermMedicationsByPatientId(patientId);
            }
            catch (error) {
                this.logger.error('Error fetching long-term medications for patient', {
                    patientId,
                    error
                });
                // Continue with empty array if fetch fails
                longTermMedications = [];
            }
        }
        // Transform long-term medications to prescription format
        const prescriptionList = (longTermMedications === null || longTermMedications === void 0 ? void 0 : longTermMedications.length)
            ? longTermMedications.map((medicationItem) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
                return {
                    id: (0, uuid_1.v4)(),
                    qty: 0,
                    name: (_b = (_a = medicationItem.medication) === null || _a === void 0 ? void 0 : _a.name) !== null && _b !== void 0 ? _b : '',
                    type: '',
                    brand: '',
                    dosage: '',
                    comment: '',
                    subList: [
                        (_d = (_c = medicationItem.medication) === null || _c === void 0 ? void 0 : _c.drug) !== null && _d !== void 0 ? _d : '',
                        (_f = (_e = medicationItem.medication) === null || _e === void 0 ? void 0 : _e.form) !== null && _f !== void 0 ? _f : '',
                        `${(_h = (_g = medicationItem.medication) === null || _g === void 0 ? void 0 : _g.strength) !== null && _h !== void 0 ? _h : ''}${(_k = (_j = medicationItem.medication) === null || _j === void 0 ? void 0 : _j.unit) !== null && _k !== void 0 ? _k : ''}`
                    ],
                    isLongTerm: true,
                    showSubList: true,
                    isRestricted: false,
                    prescriptionId: (_l = medicationItem === null || medicationItem === void 0 ? void 0 : medicationItem.medication) === null || _l === void 0 ? void 0 : _l.id,
                    chargeablePrice: (_m = medicationItem === null || medicationItem === void 0 ? void 0 : medicationItem.medication) === null || _m === void 0 ? void 0 : _m.chargeablePrice
                };
            })
            : [];
        return {
            appointmentId, // Inject appointmentId into details
            invoiceAmount: 0,
            subjective: '',
            objective: {
                vitals: [
                    {
                        time: moment().format('h:mm A'),
                        weight: '',
                        temperature: '',
                        heartRate: '',
                        respRate: '',
                        attitude: '',
                        painScore: '',
                        mucousMembrane: '',
                        capillaryRefill: '',
                        bcs: '',
                        bp: '',
                        map: ''
                    }
                ],
                physicalExam: physicalExamCategories.map(category => ({
                    id: (0, uuid_1.v4)(),
                    category,
                    status: '',
                    notes: ''
                })),
                ultrasoundExam: ultrasoundExamCategories.map(item => ({
                    id: (0, uuid_1.v4)(),
                    category: item.category,
                    status: '',
                    notes: item.notes
                })),
                bodyMaps: [],
                labReports: []
            },
            plans: {
                list: [],
                notes: ''
            },
            assessment: {
                list: [],
                notes: ''
            },
            followup: null,
            prescription: {
                list: prescriptionList,
                notes: ''
            },
            attachments: {
                list: []
            }
        };
    }
    /**
     * Emit socket updates based on call site ID to sync real-time changes
     */
    async emitSocketUpdatesForCallSite(appointmentId, callSiteId, appointmentDetails) {
        try {
            this.logger.log('Emitting socket updates for call site', {
                appointmentId,
                callSiteId
            });
            // Parse call site ID to determine what to emit
            const [baseCallSite, subCallSite] = callSiteId.split(':');
            if (baseCallSite !== 'CALL_SITE_ID_004') {
                // Only handle cart-related updates for now
                return;
            }
            // Determine what to emit based on sub call site ID
            switch (subCallSite) {
                case 'TU_72': // normal item
                case 'PDT_2017': // qty change
                case 'PDT_2018': // normal item delete
                    await this.emitPlanUpdate(appointmentId, appointmentDetails, callSiteId);
                    break;
                case 'TU_835': // labreport
                case 'TU_898': // idexx
                case 'TU_654': // labreport delete
                    await this.emitPlanUpdate(appointmentId, appointmentDetails, callSiteId);
                    await this.emitObjectiveLabReportUpdate(appointmentId, appointmentDetails, callSiteId);
                    break;
                default:
                    this.logger.log('Unknown sub call site ID, emitting plan update only', {
                        subCallSite
                    });
                    break;
            }
        }
        catch (error) {
            this.logger.error('Error emitting socket updates', {
                appointmentId,
                callSiteId,
                error
            });
        }
    }
    /**
     * Emit plan section updates
     */
    async emitPlanUpdate(appointmentId, appointmentDetails, callSiteId) {
        var _a;
        const planData = (_a = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.plans) === null || _a === void 0 ? void 0 : _a.list;
        if (planData) {
            await this.appointmentGateway.publishAppointmentUpdate(appointmentId, {
                appointmentId: appointmentId,
                key: 'plans.list',
                value: planData
            });
            this.logger.log('Emitted plan update', {
                appointmentId,
                callSiteId,
                plansEmitted: planData.length
            });
        }
        else {
            this.logger.warn('DEBUG: No plan data to emit', {
                appointmentId,
                callSiteId
            });
        }
    }
    /**
     * Emit objective lab report updates
     */
    async emitObjectiveLabReportUpdate(appointmentId, appointmentDetails, callSiteId) {
        var _a;
        const labReportData = (_a = appointmentDetails === null || appointmentDetails === void 0 ? void 0 : appointmentDetails.objective) === null || _a === void 0 ? void 0 : _a.labReports;
        if (labReportData) {
            await this.appointmentGateway.publishAppointmentUpdate(appointmentId, {
                appointmentId: appointmentId,
                key: 'objective.labReports',
                value: labReportData
            });
            this.logger.log('Emitted objective lab report update', {
                appointmentId,
                callSiteId,
                labReportsEmitted: labReportData.length
            });
        }
        else {
            this.logger.warn('DEBUG: No lab report data to emit', {
                appointmentId,
                callSiteId
            });
        }
    }
    /**
     * Send email and WhatsApp notifications when an appointment is updated or cancelled.
     * For now creation notifications stay where they are (createAppointment).
     */
    async sendAppointmentNotifications(appointment, type) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        try {
            // Ensure we have the necessary relations loaded – patient → owners, clinic, brand
            if (!appointment.patient || !appointment.patient.patientOwners) {
                this.logger.warn('sendAppointmentNotifications called without patient owners loaded', {
                    appointmentId: appointment.id,
                    type
                });
                return;
            }
            const formattedDate = moment(appointment.date).format('MMMM Do YYYY');
            const formattedTime = `${moment(appointment.startTime)
                .add(5, 'hours')
                .add(30, 'minute')
                .format('h:mm a')}`;
            for (const patientOwner of appointment.patient.patientOwners) {
                const ownerFirstName = ((_a = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName) || '';
                const ownerLastName = ((_b = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName) || '';
                const ownerMobileNumber = `${((_d = (_c = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _c === void 0 ? void 0 : _c.globalOwner) === null || _d === void 0 ? void 0 : _d.countryCode) || ''}${((_f = (_e = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _e === void 0 ? void 0 : _e.globalOwner) === null || _f === void 0 ? void 0 : _f.phoneNumber) || ''}`;
                const brandName = (_h = (_g = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _g === void 0 ? void 0 : _g.brand) === null || _h === void 0 ? void 0 : _h.name;
                const contactInformation = (_l = (_k = (_j = appointment === null || appointment === void 0 ? void 0 : appointment.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers) === null || _k === void 0 ? void 0 : _k[0]) === null || _l === void 0 ? void 0 : _l.number;
                /* ----------------------------- EMAIL NOTIFICATION ---------------------------- */
                let mailPayload = null;
                if (type === 'update') {
                    mailPayload = (0, mail_template_generator_1.appointmentUpdateMailGenerator)({
                        firstname: ownerFirstName,
                        lastName: ownerLastName,
                        updatedAppointmentDate: formattedDate,
                        updatedAppointmentTime: formattedTime,
                        brandName,
                        contactInformation,
                        petName: appointment.patient.patientName,
                        email: ((_m = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _m === void 0 ? void 0 : _m.email) || ''
                    });
                }
                else if (type === 'cancel') {
                    mailPayload = (0, mail_template_generator_1.appointmentCancelMailGenerator)({
                        firstname: ownerFirstName,
                        lastName: ownerLastName,
                        appointmentDate: formattedDate,
                        appointmentTime: formattedTime,
                        brandName,
                        contactInformation,
                        email: ((_o = patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand) === null || _o === void 0 ? void 0 : _o.email) || ''
                    });
                }
                if (mailPayload && mailPayload.toMailAddress) {
                    if ((0, get_login_url_1.isProduction)()) {
                        this.mailService.sendMail(mailPayload);
                    }
                    else if (!(0, get_login_url_1.isProduction)()) {
                        this.mailService.sendMail({
                            ...mailPayload,
                            toMailAddress: constants_1.DEV_SES_EMAIL
                        });
                    }
                }
                /* --------------------------- WHATSAPP NOTIFICATION --------------------------- */
                if (ownerMobileNumber) {
                    // Build template args common to both templates
                    const templateArgs = {
                        clientName: `${ownerFirstName} ${ownerLastName}`.trim(),
                        appointmentDate: formattedDate,
                        appointmentTime: formattedTime,
                        brandName,
                        contactInformation,
                        mobileNumber: ownerMobileNumber,
                        petName: appointment.patient.patientName
                    };
                    let templateDataFn;
                    let templateDataClinicLinkFn;
                    if (type === 'update') {
                        templateDataFn = whatsapp_template_generator_1.getAppointmentUpdateTemplateData;
                        templateDataClinicLinkFn =
                            whatsapp_template_generator_1.getAppointmentUpdateClinicLinkTemplateData;
                    }
                    else {
                        templateDataFn = whatsapp_template_generator_1.getAppointmentCancellationTemplateData;
                        templateDataClinicLinkFn =
                            whatsapp_template_generator_1.getAppointmentCancellationClinicLinkTemplateData;
                    }
                    const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(appointment.clinic, templateArgs, templateDataFn, templateDataClinicLinkFn);
                    if ((0, get_login_url_1.isProductionOrUat)()) {
                        try {
                            await this.whatsappService.sendTemplateMessage({
                                templateName,
                                valuesArray,
                                mobileNumber
                            });
                        }
                        catch (err) {
                            this.logger.error('Error sending WhatsApp message', { err });
                        }
                    }
                }
            }
        }
        catch (error) {
            this.logger.error('Error in sendAppointmentNotifications', {
                appointmentId: appointment.id,
                type,
                error
            });
        }
    }
};
exports.AppointmentsService = AppointmentsService;
__decorate([
    (0, schedule_1.Cron)('0 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppointmentsService.prototype, "handleMissedAppointments", null);
exports.AppointmentsService = AppointmentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(appointment_doctor_entity_1.AppointmentDoctorsEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(appointment_details_entity_1.AppointmentDetailsEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(9, (0, common_1.Inject)((0, common_1.forwardRef)(() => emr_service_1.EmrService))),
    __param(10, (0, common_1.Inject)((0, common_1.forwardRef)(() => sqs_service_1.SqsService))),
    __param(12, (0, common_1.Inject)((0, common_1.forwardRef)(() => availability_service_1.AvailabilityService))),
    __param(14, (0, common_1.Inject)((0, common_1.forwardRef)(() => socket_appointment_gateway_1.AppointmentGateway))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        send_mail_service_1.SESMailService,
        tasks_service_1.TasksService,
        winston_logger_service_1.WinstonLogger,
        whatsapp_service_1.WhatsappService,
        patient_reminder_service_1.PatientRemindersService,
        emr_service_1.EmrService,
        sqs_service_1.SqsService,
        global_reminders_service_1.GlobalReminderService,
        availability_service_1.AvailabilityService,
        long_term_medications_service_1.LongTermMedicationsService,
        socket_appointment_gateway_1.AppointmentGateway,
        google_calendar_service_1.GoogleCalendarService,
        google_calendar_cache_service_1.GoogleCalendarCacheService])
], AppointmentsService);
function updateTimestamps(appointment, newStatus) {
    const currentTime = new Date();
    if (newStatus === enum_appointment_status_1.EnumAppointmentStatus.Checkedin &&
        !appointment.checkinTime) {
        appointment.checkinTime = currentTime;
    }
    if (newStatus === enum_appointment_status_1.EnumAppointmentStatus.ReceivingCare &&
        !appointment.receivingCareTime) {
        appointment.receivingCareTime = currentTime;
    }
    if (newStatus === enum_appointment_status_1.EnumAppointmentStatus.Checkedout &&
        !appointment.checkoutTime) {
        appointment.checkoutTime = currentTime;
    }
    return appointment;
}
//# sourceMappingURL=appointments.service.js.map