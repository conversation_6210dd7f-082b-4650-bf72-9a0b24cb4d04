import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button, Text } from '@/app/atoms';
import { Tags } from '@/app/atoms';
import {
    useGoogleCalendarStatus,
    useGoogleCalendars,
    useConnectGoogleCalendar,
    useDisconnectGoogleCalendar,
    useGoogleCalendarAuthUrl,
} from '@/app/services/google-calendar.queries';
import { getGoogleCalendars as fetchGoogleCalendarsDirect } from '@/app/services/google-calendar.service';
import {
    GoogleCalendarStatus,
    GoogleCalendarInfo,
    GoogleAuthUrlResponse,
} from '@/app/types/google-calendar';
import {
    Calendar,
    Link,
    Unlink,
    RefreshCw,
    AlertCircle,
    Check,
} from 'lucide-react';
import moment from 'moment';
import { Section } from '@/app/atoms/Section';

interface GoogleCalendarConnectionProps {
    clinicUserId?: string;
}

const GoogleCalendarConnection: React.FC<GoogleCalendarConnectionProps> = ({
    clinicUserId,
}) => {
    const [selectedCalendarId, setSelectedCalendarId] = useState<string>('');
    const [isConnecting, setIsConnecting] = useState(false);
    const [showCalendarSelection, setShowCalendarSelection] = useState(false);
    const [hasHandledOAuth, setHasHandledOAuth] = useState(false);
    // Tracks whether we have already attempted an automatic calendar selection in the current flow
    const [hasAutoSelected, setHasAutoSelected] = useState(false);
    const [popupBlockedError, setPopupBlockedError] = useState(false);

    // Add refs to prevent excessive refetching
    const refetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const lastRefetchTimestamp = useRef<number>(0);

    // React Query hooks
    const {
        data: statusResponse,
        isLoading: isLoadingStatus,
        error: statusError,
        refetch: refetchStatus,
    } = useGoogleCalendarStatus();

    const handleCalendarsSuccess = (response: any) => {
        const fetched: GoogleCalendarInfo[] = response?.data || [];
        if (
            showCalendarSelection &&
            !hasAutoSelected &&
            fetched.length > 0 &&
            !status?.isConnected
        ) {
            const preferred = fetched.find((c) => c.primary) || fetched[0];
            if (preferred) {
                setHasAutoSelected(true);
                void handleCalendarConnect(preferred.id);
            }
        }
    };

    const {
        data: calendarsResponse,
        isLoading: isLoadingCalendars,
        error: calendarsError,
        refetch: refetchCalendars,
    } = useGoogleCalendars(showCalendarSelection, handleCalendarsSuccess);

    const {
        data: authUrlResponse,
        refetch: getAuthUrl,
        isLoading: isLoadingAuthUrl,
    } = useGoogleCalendarAuthUrl();

    const connectMutation = useConnectGoogleCalendar();
    const disconnectMutation = useDisconnectGoogleCalendar();

    const status: GoogleCalendarStatus | null = statusResponse?.data || null;
    const calendars: GoogleCalendarInfo[] = calendarsResponse?.data || [];

    // Debounced refetch function to prevent excessive API calls
    const debouncedRefetch = useCallback(() => {
        const now = Date.now();
        const timeSinceLastRefetch = now - lastRefetchTimestamp.current;

        // Prevent refetching more than once every 5 seconds
        if (timeSinceLastRefetch < 5000) {
            console.log('Skipping refetch - too soon since last refetch');
            return;
        }

        if (refetchTimeoutRef.current) {
            clearTimeout(refetchTimeoutRef.current);
        }

        refetchTimeoutRef.current = setTimeout(() => {
            console.log('Executing debounced refetch');
            lastRefetchTimestamp.current = Date.now();
            refetchStatus();
            if (showCalendarSelection) {
                refetchCalendars();
            }
        }, 1000); // 1 second debounce
    }, [refetchStatus, refetchCalendars, showCalendarSelection]);

    // Handle OAuth result via polling status instead of relying on URL params
    useEffect(() => {
        if (!isConnecting) return;

        // If backend shows we now have a refresh token (PENDING) or already connected, stop spinner
        if (status?.syncStatus === 'PENDING' || status?.isConnected) {
            setIsConnecting(false);
        }

        // If refresh token present but calendar not chosen, open selection list automatically
        if (
            status?.syncStatus === 'PENDING' &&
            !status?.isConnected &&
            !showCalendarSelection
        ) {
            setShowCalendarSelection(true);
        }
    }, [
        isConnecting,
        status?.syncStatus,
        status?.isConnected,
        showCalendarSelection,
    ]);

    // Auto-show calendar selection if user has refresh token but no calendar selected
    // Only trigger this once when status changes to avoid loops
    useEffect(() => {
        if (
            status &&
            status.syncStatus === 'PENDING' &&
            !status.isConnected &&
            !showCalendarSelection &&
            !hasHandledOAuth
        ) {
            // Attempt silent auto-connect
            (async () => {
                try {
                    const calendarsResp = await fetchGoogleCalendarsDirect();
                    const list: GoogleCalendarInfo[] =
                        calendarsResp?.data || [];
                    const preferred = list.find((c) => c.primary) || list[0];
                    if (preferred) {
                        await connectMutation.mutateAsync(preferred.id);
                        setShowCalendarSelection(false);
                        setHasAutoSelected(true);
                        debouncedRefetch();
                        return;
                    }
                } catch (err) {
                    console.error('Silent auto-connect failed', err);
                }
                // Fallback to manual selection UI
                setShowCalendarSelection(true);
            })();
        }
    }, [
        status?.syncStatus,
        status?.isConnected,
        showCalendarSelection,
        hasHandledOAuth,
        connectMutation,
        debouncedRefetch,
    ]);

    // removed auto-selection effect – now handled in onSuccess callback

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (refetchTimeoutRef.current) {
                clearTimeout(refetchTimeoutRef.current);
            }
        };
    }, []);

    const handleConnect = async () => {
        try {
            setIsConnecting(true);
            setPopupBlockedError(false); // Clear any previous popup error
            const response = await getAuthUrl();

            // Try multiple possible response structures
            let authUrl = null;
            const data = (response as any)?.data;

            // Try direct url property on response.data
            if (data?.url) {
                authUrl = data.url;
            }
            // Try nested data.data.url structure
            else if (data?.data?.url) {
                authUrl = data.data.url;
            }
            // Try if response.data itself is the URL
            else if (typeof data === 'string' && data.startsWith('http')) {
                authUrl = data;
            }

            if (authUrl) {
                // Ensure popup blockers don't interfere by opening immediately
                const popup = window.open(
                    authUrl,
                    '_blank',
                    'width=500,height=600,scrollbars=yes,resizable=yes'
                );

                if (!popup) {
                    setPopupBlockedError(true);
                    setIsConnecting(false);
                } else {
                    // Start aggressive status polling while popup is open
                    const statusPoll = setInterval(() => {
                        refetchStatus();
                    }, 4000);

                    const timer = setInterval(() => {
                        if (popup.closed) {
                            clearInterval(timer);
                            clearInterval(statusPoll);
                            setIsConnecting(false);
                            return;
                        }

                        let href: string | undefined;
                        try {
                            href = popup.location.href;
                        } catch (_) {
                            return;
                        }

                        if (href && href.includes('google-auth=')) {
                            popup.close();
                            clearInterval(timer);
                            clearInterval(statusPoll);
                            setIsConnecting(false);
                            // Immediately refetch status and calendars
                            refetchStatus();
                            if (showCalendarSelection) {
                                refetchCalendars();
                            }
                        }
                    }, 1000);
                }
            }
        } catch (error) {
            setIsConnecting(false);
        }
    };

    const handleCalendarConnect = async (calendarId: string) => {
        try {
            await connectMutation.mutateAsync(calendarId);
            setShowCalendarSelection(false);
            setTimeout(() => debouncedRefetch(), 2000); // Allow 2 seconds for backend to process
        } catch (error) {
            console.error('Failed to connect calendar:', error);
        }
    };

    const handleDisconnect = async () => {
        try {
            await disconnectMutation.mutateAsync();
            setShowCalendarSelection(false);
            setHasAutoSelected(false); // reset for the next connection attempt
            setTimeout(() => debouncedRefetch(), 2000); // Allow 2 seconds for backend to process
        } catch (error) {
            console.error('Failed to disconnect calendar:', error);
        }
    };

    const getStatusColor = (syncStatus?: string) => {
        switch (syncStatus) {
            case 'SUCCESS':
                return 'success';
            case 'FAILED':
                return 'error';
            case 'PENDING':
                return 'warning';
            default:
                return 'neutral';
        }
    };

    const getStatusText = (syncStatus?: string) => {
        switch (syncStatus) {
            case 'SUCCESS':
                return 'Synced';
            case 'FAILED':
                return 'Sync Failed';
            case 'PENDING':
                return 'Syncing...';
            default:
                return 'Not Connected';
        }
    };

    if (isLoadingStatus) {
        return (
            <Section
                title="Connect Your Google Calendar"
                showEdit={false}
                className="!p-0"
            >
                <div className="flex justify-center py-8">
                    <RefreshCw
                        size={20}
                        className="animate-spin text-primary-600"
                    />
                </div>
            </Section>
        );
    }

    // Render the appropriate action button based on connection status
    const renderActionButton = () => {
        if (!status?.isConnected) {
            if (status?.syncStatus === 'PENDING') {
                return (
                    <Button
                        id="select-google-calendar"
                        label="Select Calendar"
                        variant="primary"
                        size="mini"
                        onClick={() => {
                            setShowCalendarSelection(true);
                            refetchCalendars();
                        }}
                        icon={<Calendar size={16} />}
                        iconPosition="left"
                    />
                );
            }

            return (
                <Button
                    id="connect-google-calendar"
                    label={isConnecting ? 'Connecting...' : 'Link Calendar'}
                    variant="primary"
                    size="mini"
                    onClick={handleConnect}
                    disabled={isConnecting || isLoadingAuthUrl}
                    iconPosition="left"
                />
            );
        }

        // Connected state – show unlink button
        return (
            <Button
                id="disconnect-google-calendar"
                label="Unlink Calendar"
                variant="secondary"
                size="mini"
                onClick={handleDisconnect}
                disabled={disconnectMutation.isPending}
                icon={<Unlink size={16} />}
                iconPosition="left"
            />
        );
    };

    return (
        <Section
            title="Connect Your Google Calendar"
            actionComponent={renderActionButton()}
            showEdit={false}
            className="!p-0 mb-2"
        >
            {/* Connection Status */}
            <div className="space-y-3">
                {/* <div className="flex items-center justify-between">
                    <Text variant="bodySmall" className="text-primary-700">
                        Status:
                    </Text>
                    <Tags
                        variant={getStatusColor(status?.syncStatus)}
                        isLight={true}
                        size="small"
                        label={getStatusText(status?.syncStatus)}
                        className="text-xs"
                    />
                </div> */}

                {status?.isConnected && (
                    <>
                        <div className="flex items-center justify-between">
                            <Text
                                variant="bodySmall"
                                className="text-primary-700"
                            >
                                Calendar:
                            </Text>
                            <Text variant="bodySmall" fontWeight="font-medium">
                                {status.calendarName || 'Unknown Calendar'}
                            </Text>
                        </div>

                        {status.lastSyncAt && (
                            <div className="flex items-center justify-between">
                                <Text
                                    variant="bodySmall"
                                    className="text-primary-700"
                                >
                                    Last Sync:
                                </Text>
                                <Text variant="bodySmall">
                                    {moment(status.lastSyncAt).format(
                                        'MMM DD, HH:mm'
                                    )}
                                </Text>
                            </div>
                        )}
                    </>
                )}

                {status?.errorMessage && (
                    <div className="flex items-start gap-2 p-2 bg-error-10 rounded">
                        <AlertCircle
                            size={16}
                            className="text-error-600 mt-0.5 flex-shrink-0"
                        />
                        <Text variant="bodySmall" className="text-error-700">
                            {status.errorMessage}
                        </Text>
                    </div>
                )}

                {popupBlockedError && (
                    <div className="flex items-start gap-2 p-2 bg-warning-10 rounded">
                        <AlertCircle
                            size={16}
                            className="text-warning-600 mt-0.5 flex-shrink-0"
                        />
                        <div className="flex-1">
                            <Text
                                variant="bodySmall"
                                className="text-warning-700"
                            >
                                Pop-ups are blocked. Please allow pop-ups for
                                this site and try again.
                            </Text>
                            <Button
                                id="dismiss-popup-error"
                                label="Dismiss"
                                variant="link"
                                size="mini"
                                onClick={() => setPopupBlockedError(false)}
                                className="mt-1 text-warning-700 hover:text-warning-800"
                            />
                        </div>
                    </div>
                )}

                {/* Sync Now button inside body when connected */}
                {status?.isConnected && (
                    <div className="pt-2">
                        <Button
                            id="sync-google-calendar"
                            label="Sync Now"
                            variant="secondary"
                            size="mini"
                            onClick={() => refetchStatus()}
                            icon={<RefreshCw size={16} />}
                            iconPosition="left"
                            className="w-full"
                        />
                    </div>
                )}

                {/* Calendar Selection Modal-like section */}
                {showCalendarSelection && (
                    <div className="mt-4 p-3 bg-secondary-10">
                        <Text
                            variant="bodySmall"
                            fontWeight="font-medium"
                            className="mb-2"
                        >
                            Select a calendar to connect:
                        </Text>

                        {/* Loading state for calendars */}
                        {isLoadingCalendars && (
                            <div className="flex items-center justify-center py-6">
                                <RefreshCw
                                    size={20}
                                    className="animate-spin text-primary-600 mr-2"
                                />
                                <Text
                                    variant="bodySmall"
                                    className="text-primary-600"
                                >
                                    Loading your calendars...
                                </Text>
                            </div>
                        )}

                        {/* Calendar list */}
                        {!isLoadingCalendars && calendars.length > 0 && (
                            <div className="space-y-2">
                                {calendars.map((calendar) => (
                                    <div
                                        key={calendar.id}
                                        className={`flex items-center justify-between p-2 bg-white rounded border transition-all ${
                                            connectMutation.isPending
                                                ? 'opacity-50 cursor-not-allowed'
                                                : 'cursor-pointer hover:bg-secondary-10'
                                        }`}
                                        onClick={() => {
                                            if (!connectMutation.isPending) {
                                                handleCalendarConnect(
                                                    calendar.id
                                                );
                                            }
                                        }}
                                    >
                                        <div>
                                            <Text
                                                variant="bodySmall"
                                                fontWeight="font-medium"
                                            >
                                                {calendar.summary}
                                            </Text>
                                            {calendar.description && (
                                                <Text
                                                    variant="caption"
                                                    className="text-primary-600"
                                                >
                                                    {calendar.description}
                                                </Text>
                                            )}
                                            {calendar.primary && (
                                                <Tags
                                                    variant="info"
                                                    isLight={true}
                                                    size="small"
                                                    label="Primary"
                                                    className="text-xs mt-1"
                                                />
                                            )}
                                        </div>
                                        {connectMutation.isPending ? (
                                            <RefreshCw
                                                size={16}
                                                className="animate-spin text-primary-600"
                                            />
                                        ) : (
                                            <Check
                                                size={16}
                                                className="text-primary-600"
                                            />
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* No calendars found */}
                        {!isLoadingCalendars && calendars.length === 0 && (
                            <div className="flex items-center justify-center py-6">
                                <AlertCircle
                                    size={20}
                                    className="text-warning-600 mr-2"
                                />
                                <Text
                                    variant="bodySmall"
                                    className="text-warning-700"
                                >
                                    No calendars found. Please check your Google
                                    Calendar access.
                                </Text>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </Section>
    );
};

export default GoogleCalendarConnection;
